Absolutely! Here’s a comprehensive prompt for building an end-to-end replica of InteriorAI (https://interiorai.com/), covering every aspect—frontend, backend, UX, image processing, user accounts, payments, security, and more. This is extremely detailed, with each feature and UI/UX flow described, plus clear notes on any limitations for what's unbuildable due to closed tech/external services.

---

## Prompt to Build InteriorAI Clone - End-to-End Specification

### 1. **Overall Goal**
Build a web application inspired by https://interiorai.com/. It enables users to upload images of interiors (room photos), use AI to generate new interior design styles for their space, and optionally pay for higher-resolution outputs or subscriptions. Site must be mobile-first, fast, and highly visual, with clear, modern branding.

---

### 2. **Frontend Requirements**

#### **General Framework & Architecture**
- **React + Next.js** for SPA/SSR.
- **TypeScript** throughout.
- **TailwindCSS or shadcn/ui** for modern, customizable design system.
- **Mobile-first**, responsive, with dark/light mode toggle.
- **SEO**, accessibility, and performance best practices.

#### **Homepage**
- Eye-catching hero section w/ fullscreen image or subtle animation.
- Clear tagline (“AI-powered interior design. See your room in any style.”)
- Strong call to action to upload a room photo.
- Explanation section: “How It Works” in 3-4 steps, each with icon.
- Testimonials carousel/slider (placeholder profiles).
- Pricing snippet or “Get Started Free” button, sticky/floating at bottom for mobile.

#### **User Onboarding & Upload**
- Drag & drop + click-to-upload for interior room images.
- Image validation: filetype, size, resolution, aspect ratio.
- Custom loader during upload/processing.
- Option to skip sign up for basic free processing.
- Modal prompting for login/signup after ~2 uses or for HD output.

#### **Room Image Preview & Design Selection**
- Show uploaded room image with cropping/adjustment tool (basic rotation, flip, crop).
- **Style selection grid**: Cards for each design style (e.g. Modern, Minimalist, Scandinavian, Industrial, etc), each with preview image.
- Option for users to enter “custom AI prompt” or style description.

#### **AI Processing UI**
- Progress bar and dynamic tips/messages during AI processing.
- Result gallery: The generated room mockups, displayed in a horizontal scroll.
- Download and “Compare with original” toggle.
- Watermark for free/low-res results.
- “Generate again” and “Try different style” buttons.

#### **User Account & History**
- Sign up with Google, Apple, or email/password.
- Basic profile (name, email, subscription status, avatar).
- History page: Grid of all uploads and corresponding generated images.
- Easy-to-use “re-generate” button for old uploads.

#### **Monetization**
- Checkout/paywall page for HD download, unlimited generations, removing watermark, or pro features.
- Stripe integration (credit card, Apple Pay, Google Pay).
- Subscription management (change/cancel plan, see next billing).
- Receipt page & emails.

#### **Other Pages**
- About, FAQ, Contact, Legal/Privacy/Terms.
- Admin mode: Dashboard with analytics (low fidelity), customer support chat link (e.g. Intercom, link only).

#### **General UX**
- Error states for failed uploads or processing.
- Toast notifications for all important actions.
- Help tooltips and FAQ popups.
- Support modal (email/form).

#### **Brand & Content**
- Use placeholder logo (“InteriorAI”) but all assets to be easly swappable.
- Consistent color palette (neutral background, accent color for calls to action).
- Fonts to match modern interior design brands—e.g. Inter UI, Plus Jakarta Sans, or similar.

---

### 3. **Backend Requirements**

#### **Core Tech Stack**
- **Node.js** (Express or Next.js API routes).
- **Bun** runtime preferred.
- **PostgreSQL** for all persistent storage (users, uploads, generations, subscriptions).
- **Prisma** ORM for schema, migrations, queries.
- **Auth**: JWT-based for sessions, with secure password hashing.
- **File storage**: Integration with AWS S3 (or local for dev) for all uploads/results.

#### **AI Generation**
- **Stable Diffusion** or other open-source model for image-to-image generation.
    - Integrate via server API (use [Replicate](https://replicate.com/) or self-hosted SD via Python subprocess).
    - Pass input room image + style prompt, return generated room images.
    - Queue process, track and show status to frontend.

#### **APIs**
- Image upload, validation, and storage endpoint.
- Generation requests (room + style + prompt).
- Queue/check status for async AI job.
- User signup/login, token refresh, etc.
- Payments: endpoints for Stripe webhook, purchase validation.
- User data: upload list/history, usage count, download rights.

#### **Admin APIs**
- User management (ban/user lookup), generations stats, feedback collection.

#### **Email**
- Welcome, password reset, purchase confirmation, admin notifications via third-party (e.g. SendGrid).

#### **Security**
- All endpoints protected against abuse (rate limiting; validation).
- Users can only view their own uploads/history.
- Processing must be sandboxed and not run arbitrary user code.
- HTTPS enforced (no sensitive info in logs).

---

### 4. **AI/ML Components**

- **Core image generation:**
    - Take room image + design style as input.
    - Run through Stable Diffusion or similar image-to-image model.
    - Apply upscaling for HD results (Real-ESRGAN or similar, optional).
    - Add small watermark for free results.

- **Note:** You may not be able to fully replicate all training or proprietary AI models of InteriorAI; use best available open-source alternatives.

---

### 5. **Optional/Nice to Have**
- Social sharing (X/Twitter, Instagram, Facebook) of generated images.
- API for programmatic access (make-your-own app).
- Multi-language support (i18n).
- Feedback button (users can flag bad generations).
- 2FA and email verification.
- Blog/CMS (optional).

---

### 6. **Out of Scope / Limitations / Not Buildable**
- Exact proprietary AI models behind InteriorAI (use open source or APIs such as Replicate).
- Very advanced interior-specific AI features (such as furniture identification, true 3D room remodelling, etc).
- Downloading copyrighted/paid theme packs or images.
- Native/hardware mobile apps (web only).
- 3D room planning or visualization.
- Integrations with professional interior design suppliers.

---

## **Summary**

“You are to build a full-stack web app that allows users to upload shots of their room, select a new interior style, and get AI-generated results. The full system must include a modern frontend (Next.js + TypeScript + Tailwind/shadcn), login/account system, image upload and history, AI backend (Stable Diffusion or Replicate API), a Stripe-powered paywall for pro features, basic admin dashboard, and robust cloud file handling. Follow all standards for UX, security, error-handling, and scalability. For all AI/ML and internal design assets, use best-available open-source tools and visually close approximations. Deliver the project with production-ready code, designed for easy customization and real-world usage.”

---

If you want me to turn this specification directly into a working codebase and start scaffolding each feature, just tell me your preferred stack (for example, shadcn vs Tailwind) or let me start with a recommended Next.js + shadcn template.
-- RoomAI Database Schema
-- Phase 1: Core Tables

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    subscription_tier VARCHAR(50) DEFAULT 'free',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Styles table (40+ interior design styles)
CREATE TABLE styles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    preview_image VARCHAR(500),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Room types table
CREATE TABLE room_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT
);

-- Color palettes table
CREATE TABLE color_palettes (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    colors_json JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Materials table
CREATE TABLE materials (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50),
    preview_image VARCHAR(500),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Projects table
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    room_type_id INTEGER REFERENCES room_types(id),
    status VARCHAR(50) DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Designs table
CREATE TABLE designs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    style_ids INTEGER[] DEFAULT '{}',
    color_palette_id INTEGER REFERENCES color_palettes(id),
    material_ids INTEGER[] DEFAULT '{}',
    input_image_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Generations table (AI results)
CREATE TABLE generations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    design_id UUID REFERENCES designs(id) ON DELETE CASCADE,
    output_image_url VARCHAR(500),
    status VARCHAR(50) DEFAULT 'pending',
    ai_model VARCHAR(100),
    generation_time INTEGER, -- in seconds
    created_at TIMESTAMP DEFAULT NOW()
);

-- Subscriptions table
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    plan_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'active',
    billing_cycle VARCHAR(20) DEFAULT 'monthly',
    stripe_subscription_id VARCHAR(255),
    current_period_start TIMESTAMP,
    current_period_end TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Usage tracking table
CREATE TABLE usage_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    action_type VARCHAR(50) NOT NULL, -- 'generation', 'upload', etc.
    month_year VARCHAR(7) NOT NULL, -- 'YYYY-MM'
    count INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, action_type, month_year)
);

-- Insert default room types
INSERT INTO room_types (name, description) VALUES
('Living Room', 'Main living and entertainment space'),
('Bedroom', 'Sleeping and personal space'),
('Kitchen', 'Cooking and dining preparation area'),
('Bathroom', 'Personal hygiene and bathing space'),
('Dining Room', 'Formal dining and entertaining space'),
('Home Office', 'Work and study space'),
('Kids Room', 'Children''s bedroom and play area'),
('Media Room', 'Entertainment and media consumption space'),
('Mudroom', 'Entry and storage space'),
('Patio', 'Outdoor living and entertainment space'),
('Laundry Room', 'Washing and utility space'),
('Home Gym', 'Exercise and fitness space');

-- Insert default styles (sample - you'll need all 40+)
INSERT INTO styles (name, description, preview_image) VALUES
('Minimalist', 'Clean lines, neutral colors, uncluttered spaces', 'https://ext.same-assets.com/4123950039/1267053207.jpeg'),
('Scandinavian', 'Light woods, cozy textures, functional design', 'https://ext.same-assets.com/4123950039/4031576135.jpeg'),
('Industrial', 'Raw materials, exposed elements, urban aesthetic', 'https://ext.same-assets.com/4123950039/2311013961.jpeg'),
('Mid-Century Modern', 'Retro furniture, bold colors, geometric patterns', 'https://ext.same-assets.com/4123950039/4247774748.jpeg'),
('Bohemian', 'Eclectic mix, rich textures, global influences', 'https://ext.same-assets.com/4123950039/2162116070.jpeg');

-- Insert default color palettes
INSERT INTO color_palettes (name, colors_json) VALUES
('Soft Neutrals', '["#F5F5F5", "#E8E8E8", "#D3D3D3", "#C0C0C0", "#A9A9A9"]'),
('Coastal Calm', '["#E6F3FF", "#B3D9FF", "#80BFFF", "#4DA6FF", "#1A8CFF"]'),
('Nordic Lights', '["#FFFFFF", "#F0F0F0", "#E0E0E0", "#D0D0D0", "#C0C0C0"]'),
('Forest Retreat', '["#2D5016", "#4A7C59", "#68A357", "#86C56A", "#A4E87D"]');

-- Insert default materials
INSERT INTO materials (name, category, preview_image) VALUES
('Oak Wood', 'Wood', 'https://example.com/oak.jpg'),
('Walnut Wood', 'Wood', 'https://example.com/walnut.jpg'),
('Steel', 'Metal', 'https://example.com/steel.jpg'),
('Chrome', 'Metal', 'https://example.com/chrome.jpg'),
('Marble', 'Stone', 'https://example.com/marble.jpg'),
('Granite', 'Stone', 'https://example.com/granite.jpg'),
('Linen', 'Fabric', 'https://example.com/linen.jpg'),
('Velvet', 'Fabric', 'https://example.com/velvet.jpg');

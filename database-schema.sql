-- RoomAI Database Schema
-- Phase 1: Core Tables

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    subscription_tier VARCHAR(50) DEFAULT 'free',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Styles table (40+ interior design styles)
CREATE TABLE styles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    preview_image VARCHAR(500),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Room types table
CREATE TABLE room_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT
);

-- Color palettes table
CREATE TABLE color_palettes (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    colors_json JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Materials table
CREATE TABLE materials (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50),
    preview_image VARCHAR(500),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Projects table
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    room_type_id INTEGER REFERENCES room_types(id),
    status VARCHAR(50) DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Designs table
CREATE TABLE designs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    style_ids INTEGER[] DEFAULT '{}',
    color_palette_id INTEGER REFERENCES color_palettes(id),
    material_ids INTEGER[] DEFAULT '{}',
    input_image_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Generations table (AI results)
CREATE TABLE generations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    design_id UUID REFERENCES designs(id) ON DELETE CASCADE,
    output_image_url VARCHAR(500),
    status VARCHAR(50) DEFAULT 'pending',
    ai_model VARCHAR(100),
    generation_time INTEGER, -- in seconds
    created_at TIMESTAMP DEFAULT NOW()
);

-- Subscriptions table
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    plan_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'active',
    billing_cycle VARCHAR(20) DEFAULT 'monthly',
    stripe_subscription_id VARCHAR(255),
    current_period_start TIMESTAMP,
    current_period_end TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Usage tracking table
CREATE TABLE usage_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    action_type VARCHAR(50) NOT NULL, -- 'generation', 'upload', etc.
    month_year VARCHAR(7) NOT NULL, -- 'YYYY-MM'
    count INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, action_type, month_year)
);

-- Insert all room types
INSERT INTO room_types (name, description) VALUES
('Living Room', 'Main living and entertainment space'),
('Bedroom', 'Sleeping and personal space'),
('Kitchen', 'Cooking and dining preparation area'),
('Bathroom', 'Personal hygiene and bathing space'),
('Dining Room', 'Formal dining and entertaining space'),
('Home Office', 'Work and study space'),
('Kids Room', 'Children''s bedroom and play area'),
('Media Room', 'Entertainment and media consumption space'),
('Mudroom', 'Entry and storage space'),
('Patio', 'Outdoor living and entertainment space'),
('Laundry Room', 'Washing and utility space'),
('Home Gym', 'Exercise and fitness space'),
('Guest Room', 'Temporary accommodation space'),
('Walk-in Closet', 'Clothing storage and dressing area'),
('Basement', 'Lower level multipurpose space'),
('Attic', 'Upper level storage or living space'),
('Sunroom', 'Glass-enclosed seasonal living space'),
('Library', 'Reading and book storage space'),
('Game Room', 'Recreation and entertainment space'),
('Wine Cellar', 'Wine storage and tasting area'),
('Nursery', 'Baby and infant care space'),
('Pantry', 'Food storage and organization area'),
('Foyer', 'Entry and welcome space'),
('Hallway', 'Connecting passage space'),
('Balcony', 'Outdoor extension space'),
('Garage', 'Vehicle storage and workshop space');

-- Insert all 40+ styles
INSERT INTO styles (name, description, preview_image) VALUES
('Minimalist', 'Clean lines, neutral colors, uncluttered spaces', 'https://ext.same-assets.com/4123950039/1267053207.jpeg'),
('Scandinavian', 'Light woods, cozy textures, functional design', 'https://ext.same-assets.com/4123950039/4031576135.jpeg'),
('Industrial', 'Raw materials, exposed elements, urban aesthetic', 'https://ext.same-assets.com/4123950039/2311013961.jpeg'),
('Mid-Century Modern', 'Retro furniture, bold colors, geometric patterns', 'https://ext.same-assets.com/4123950039/4247774748.jpeg'),
('Bohemian', 'Eclectic mix, rich textures, global influences', 'https://ext.same-assets.com/4123950039/2162116070.jpeg'),
('Art Deco', 'Geometric patterns, luxurious materials, bold colors', 'https://ext.same-assets.com/4123950039/3217488218.jpeg'),
('Coastal', 'Light colors, natural textures, beach-inspired elements', 'https://ext.same-assets.com/4123950039/4253859114.jpeg'),
('Farmhouse', 'Rustic charm, vintage elements, cozy comfort', 'https://ext.same-assets.com/4123950039/2462314040.jpeg'),
('Traditional', 'Classic furniture, rich fabrics, timeless elegance', 'https://ext.same-assets.com/4123950039/362001557.jpeg'),
('French Country', 'Romantic charm, soft colors, vintage French elements', 'https://ext.same-assets.com/4123950039/690654409.jpeg'),
('Moroccan', 'Rich patterns, vibrant colors, exotic textures', 'https://ext.same-assets.com/4123950039/2521446536.jpeg'),
('Modern', 'Sleek lines, contemporary materials, functional design', 'https://ext.same-assets.com/4123950039/1390157197.jpeg'),
('Transitional', 'Blend of traditional and contemporary elements', 'https://ext.same-assets.com/4123950039/891312444.jpeg'),
('Shabby Chic', 'Distressed furniture, soft pastels, vintage romance', 'https://ext.same-assets.com/4123950039/1011160405.jpeg'),
('Japanese Zen', 'Simplicity, natural materials, peaceful harmony', 'https://ext.same-assets.com/4123950039/3933773814.jpeg'),
('Contemporary', 'Current trends, clean lines, open spaces', 'https://ext.same-assets.com/4123950039/3246906598.jpeg'),
('Gothic', 'Dark colors, dramatic elements, medieval inspiration', 'https://ext.same-assets.com/4123950039/1038660028.jpeg'),
('Hollywood Regency', 'Glamorous luxury, bold patterns, metallic accents', 'https://ext.same-assets.com/4123950039/927860819.jpeg'),
('Rustic', 'Natural materials, earthy tones, countryside charm', 'https://ext.same-assets.com/4123950039/1650356877.jpeg'),
('Southwestern', 'Desert colors, Native American influences, warm textures', 'https://ext.same-assets.com/4123950039/2422832776.jpeg'),
('Victorian', 'Ornate details, rich fabrics, historical elegance', 'https://ext.same-assets.com/4123950039/180188305.jpeg'),
('Steampunk', 'Industrial vintage, brass accents, mechanical elements', 'https://ext.same-assets.com/4123950039/650580268.jpeg'),
('Biophilic', 'Nature integration, living plants, organic materials', 'https://ext.same-assets.com/4123950039/3597336419.jpeg'),
('Futuristic', 'High-tech materials, sleek surfaces, space-age design', 'https://ext.same-assets.com/4123950039/1432023951.jpeg'),
('Maximalist', 'Bold patterns, rich colors, eclectic collections', 'https://ext.same-assets.com/4123950039/1389072092.jpeg'),
('Pop Art', 'Bright colors, graphic elements, playful design', 'https://ext.same-assets.com/4123950039/3540851178.jpeg'),
('Cosmic Chic', 'Celestial themes, metallic accents, dreamy atmosphere', 'https://ext.same-assets.com/4123950039/3420677188.jpeg'),
('Wabi-Sabi', 'Imperfect beauty, natural aging, mindful simplicity', 'https://ext.same-assets.com/4123950039/744976908.jpeg'),
('Tropical', 'Lush greens, natural textures, resort-like atmosphere', 'https://ext.same-assets.com/4123950039/2401809942.jpeg'),
('Vintage Glam', 'Retro luxury, metallic finishes, sophisticated charm', 'https://ext.same-assets.com/4123950039/1246017528.jpeg'),
('Cyberpunk', 'Neon colors, high-tech elements, futuristic edge', 'https://ext.same-assets.com/4123950039/2253538115.jpeg'),
('Psychedelic', 'Vibrant patterns, bold colors, mind-bending design', 'https://ext.same-assets.com/4123950039/3683077406.jpeg'),
('Surrealist', 'Dreamlike elements, unexpected combinations, artistic flair', 'https://ext.same-assets.com/4123950039/1098851357.jpeg'),
('Post-Apocalyptic', 'Distressed materials, survival aesthetic, raw textures', 'https://ext.same-assets.com/4123950039/576171152.jpeg'),
('Candy Land', 'Sweet colors, playful elements, whimsical design', 'https://ext.same-assets.com/4123950039/3193769849.jpeg'),
('Mediterranean', 'Warm colors, natural stone, coastal European charm'),
('Asian Fusion', 'Eastern influences, balanced elements, cultural blend'),
('Eclectic', 'Mixed styles, personal collections, creative combinations'),
('Glam', 'Luxurious materials, metallic accents, sophisticated drama'),
('Retro', 'Vintage revival, nostalgic elements, period-specific design'),
('Urban Loft', 'Open spaces, exposed elements, city living aesthetic'),
('Country Cottage', 'Cozy comfort, floral patterns, rural charm'),
('Art Nouveau', 'Organic forms, flowing lines, nature-inspired motifs'),
('Bauhaus', 'Functional design, geometric forms, modernist principles'),
('Memphis', 'Bold geometry, bright colors, 1980s postmodern style');

-- Insert comprehensive color palettes
INSERT INTO color_palettes (name, colors_json) VALUES
('Soft Neutrals', '["#F5F5F5", "#E8E8E8", "#D3D3D3", "#C0C0C0", "#A9A9A9"]'),
('Coastal Calm', '["#E6F3FF", "#B3D9FF", "#80BFFF", "#4DA6FF", "#1A8CFF"]'),
('Nordic Lights', '["#FFFFFF", "#F0F0F0", "#E0E0E0", "#D0D0D0", "#C0C0C0"]'),
('Forest Retreat', '["#2D5016", "#4A7C59", "#68A357", "#86C56A", "#A4E87D"]'),
('Warm Earth', '["#8B4513", "#CD853F", "#DEB887", "#F4A460", "#FFE4B5"]'),
('Ocean Breeze', '["#006994", "#0085C3", "#00A1C9", "#00BFFF", "#87CEEB"]'),
('Sunset Glow', '["#FF4500", "#FF6347", "#FF7F50", "#FFA07A", "#FFB6C1"]'),
('Lavender Dreams', '["#663399", "#8A2BE2", "#9370DB", "#BA55D3", "#DDA0DD"]'),
('Monochrome Classic', '["#000000", "#404040", "#808080", "#C0C0C0", "#FFFFFF"]'),
('Sage & Stone', '["#9CAF88", "#B8C5A6", "#D4DBC4", "#F0F1E2", "#FFFFFF"]'),
('Terracotta Warmth', '["#A0522D", "#CD853F", "#DEB887", "#F5DEB3", "#FFF8DC"]'),
('Deep Ocean', '["#003366", "#004080", "#0066CC", "#3399FF", "#66B2FF"]'),
('Blush & Gold', '["#FFB6C1", "#FFC0CB", "#FFD700", "#FFFFE0", "#FFFACD"]'),
('Emerald Forest', '["#013220", "#355E3B", "#50C878", "#90EE90", "#F0FFF0"]'),
('Midnight Blue', '["#191970", "#000080", "#0000CD", "#4169E1", "#6495ED"]'),
('Autumn Harvest', '["#8B4513", "#A0522D", "#CD853F", "#DEB887", "#F5DEB3"]'),
('Rose Garden', '["#8B0000", "#DC143C", "#FF1493", "#FF69B4", "#FFB6C1"]'),
('Arctic Ice', '["#B0E0E6", "#E0FFFF", "#F0F8FF", "#F8F8FF", "#FFFFFF"]'),
('Desert Sand', '["#8B7355", "#D2B48C", "#F4A460", "#FAD5A5", "#FFEFD5"]'),
('Tropical Paradise', '["#228B22", "#32CD32", "#7CFC00", "#ADFF2F", "#F0FFF0"]'),
('Vintage Rose', '["#B76E79", "#D4A5A5", "#E8C5C5", "#F2E5E5", "#FFFFFF"]'),
('Industrial Gray', '["#2F4F4F", "#696969", "#808080", "#A9A9A9", "#D3D3D3"]'),
('Citrus Burst', '["#FF4500", "#FF6347", "#FFA500", "#FFD700", "#FFFF00"]'),
('Royal Purple', '["#4B0082", "#663399", "#8A2BE2", "#9370DB", "#DDA0DD"]'),
('Fresh Mint', '["#00FF7F", "#98FB98", "#90EE90", "#F0FFF0", "#FFFFFF"]'),
('Chocolate Brown', '["#3C1810", "#654321", "#8B4513", "#A0522D", "#D2B48C"]'),
('Peacock Blue', '["#005F69", "#008B8B", "#20B2AA", "#48D1CC", "#AFEEEE"]'),
('Coral Reef', '["#FF5722", "#FF7043", "#FF8A65", "#FFAB91", "#FFCCBC"]'),
('Stormy Sky', '["#2F4F4F", "#708090", "#778899", "#B0C4DE", "#E6E6FA"]'),
('Golden Hour', '["#B8860B", "#DAA520", "#FFD700", "#FFFF00", "#FFFACD"]');

-- Insert comprehensive materials
INSERT INTO materials (name, category, preview_image) VALUES
-- Wood Materials
('Oak Wood', 'Wood', 'https://example.com/materials/oak.jpg'),
('Walnut Wood', 'Wood', 'https://example.com/materials/walnut.jpg'),
('Cherry Wood', 'Wood', 'https://example.com/materials/cherry.jpg'),
('Maple Wood', 'Wood', 'https://example.com/materials/maple.jpg'),
('Birch Wood', 'Wood', 'https://example.com/materials/birch.jpg'),
('Pine Wood', 'Wood', 'https://example.com/materials/pine.jpg'),
('Mahogany Wood', 'Wood', 'https://example.com/materials/mahogany.jpg'),
('Teak Wood', 'Wood', 'https://example.com/materials/teak.jpg'),
('Bamboo', 'Wood', 'https://example.com/materials/bamboo.jpg'),
('Reclaimed Wood', 'Wood', 'https://example.com/materials/reclaimed.jpg'),
('Driftwood', 'Wood', 'https://example.com/materials/driftwood.jpg'),
('Ebony Wood', 'Wood', 'https://example.com/materials/ebony.jpg'),

-- Metal Materials
('Stainless Steel', 'Metal', 'https://example.com/materials/steel.jpg'),
('Chrome', 'Metal', 'https://example.com/materials/chrome.jpg'),
('Brass', 'Metal', 'https://example.com/materials/brass.jpg'),
('Copper', 'Metal', 'https://example.com/materials/copper.jpg'),
('Bronze', 'Metal', 'https://example.com/materials/bronze.jpg'),
('Iron', 'Metal', 'https://example.com/materials/iron.jpg'),
('Aluminum', 'Metal', 'https://example.com/materials/aluminum.jpg'),
('Gold Finish', 'Metal', 'https://example.com/materials/gold.jpg'),
('Silver Finish', 'Metal', 'https://example.com/materials/silver.jpg'),
('Pewter', 'Metal', 'https://example.com/materials/pewter.jpg'),
('Titanium', 'Metal', 'https://example.com/materials/titanium.jpg'),
('Wrought Iron', 'Metal', 'https://example.com/materials/wrought-iron.jpg'),

-- Stone Materials
('Marble', 'Stone', 'https://example.com/materials/marble.jpg'),
('Granite', 'Stone', 'https://example.com/materials/granite.jpg'),
('Limestone', 'Stone', 'https://example.com/materials/limestone.jpg'),
('Travertine', 'Stone', 'https://example.com/materials/travertine.jpg'),
('Slate', 'Stone', 'https://example.com/materials/slate.jpg'),
('Quartzite', 'Stone', 'https://example.com/materials/quartzite.jpg'),
('Sandstone', 'Stone', 'https://example.com/materials/sandstone.jpg'),
('Onyx', 'Stone', 'https://example.com/materials/onyx.jpg'),
('Basalt', 'Stone', 'https://example.com/materials/basalt.jpg'),
('River Rock', 'Stone', 'https://example.com/materials/river-rock.jpg'),
('Fieldstone', 'Stone', 'https://example.com/materials/fieldstone.jpg'),
('Cobblestone', 'Stone', 'https://example.com/materials/cobblestone.jpg'),

-- Fabric Materials
('Linen', 'Fabric', 'https://example.com/materials/linen.jpg'),
('Cotton', 'Fabric', 'https://example.com/materials/cotton.jpg'),
('Velvet', 'Fabric', 'https://example.com/materials/velvet.jpg'),
('Silk', 'Fabric', 'https://example.com/materials/silk.jpg'),
('Wool', 'Fabric', 'https://example.com/materials/wool.jpg'),
('Cashmere', 'Fabric', 'https://example.com/materials/cashmere.jpg'),
('Leather', 'Fabric', 'https://example.com/materials/leather.jpg'),
('Suede', 'Fabric', 'https://example.com/materials/suede.jpg'),
('Chenille', 'Fabric', 'https://example.com/materials/chenille.jpg'),
('Tweed', 'Fabric', 'https://example.com/materials/tweed.jpg'),
('Burlap', 'Fabric', 'https://example.com/materials/burlap.jpg'),
('Canvas', 'Fabric', 'https://example.com/materials/canvas.jpg'),
('Denim', 'Fabric', 'https://example.com/materials/denim.jpg'),
('Faux Fur', 'Fabric', 'https://example.com/materials/faux-fur.jpg'),

-- Glass Materials
('Clear Glass', 'Glass', 'https://example.com/materials/clear-glass.jpg'),
('Frosted Glass', 'Glass', 'https://example.com/materials/frosted-glass.jpg'),
('Tempered Glass', 'Glass', 'https://example.com/materials/tempered-glass.jpg'),
('Stained Glass', 'Glass', 'https://example.com/materials/stained-glass.jpg'),
('Mirrored Glass', 'Glass', 'https://example.com/materials/mirrored-glass.jpg'),
('Textured Glass', 'Glass', 'https://example.com/materials/textured-glass.jpg'),
('Colored Glass', 'Glass', 'https://example.com/materials/colored-glass.jpg'),
('Laminated Glass', 'Glass', 'https://example.com/materials/laminated-glass.jpg'),

-- Ceramic Materials
('Porcelain', 'Ceramic', 'https://example.com/materials/porcelain.jpg'),
('Ceramic Tile', 'Ceramic', 'https://example.com/materials/ceramic-tile.jpg'),
('Terra Cotta', 'Ceramic', 'https://example.com/materials/terra-cotta.jpg'),
('Stoneware', 'Ceramic', 'https://example.com/materials/stoneware.jpg'),
('Earthenware', 'Ceramic', 'https://example.com/materials/earthenware.jpg'),
('Glazed Ceramic', 'Ceramic', 'https://example.com/materials/glazed-ceramic.jpg'),

-- Synthetic Materials
('Acrylic', 'Synthetic', 'https://example.com/materials/acrylic.jpg'),
('Plastic', 'Synthetic', 'https://example.com/materials/plastic.jpg'),
('Vinyl', 'Synthetic', 'https://example.com/materials/vinyl.jpg'),
('Laminate', 'Synthetic', 'https://example.com/materials/laminate.jpg'),
('Fiberglass', 'Synthetic', 'https://example.com/materials/fiberglass.jpg'),
('Carbon Fiber', 'Synthetic', 'https://example.com/materials/carbon-fiber.jpg'),
('Resin', 'Synthetic', 'https://example.com/materials/resin.jpg'),
('Polyurethane', 'Synthetic', 'https://example.com/materials/polyurethane.jpg'),

-- Natural Materials
('Rattan', 'Natural', 'https://example.com/materials/rattan.jpg'),
('Wicker', 'Natural', 'https://example.com/materials/wicker.jpg'),
('Jute', 'Natural', 'https://example.com/materials/jute.jpg'),
('Sisal', 'Natural', 'https://example.com/materials/sisal.jpg'),
('Cork', 'Natural', 'https://example.com/materials/cork.jpg'),
('Seagrass', 'Natural', 'https://example.com/materials/seagrass.jpg'),
('Hemp', 'Natural', 'https://example.com/materials/hemp.jpg'),
('Cane', 'Natural', 'https://example.com/materials/cane.jpg'),
('Rush', 'Natural', 'https://example.com/materials/rush.jpg'),

-- Composite Materials
('Quartz Composite', 'Composite', 'https://example.com/materials/quartz-composite.jpg'),
('Engineered Stone', 'Composite', 'https://example.com/materials/engineered-stone.jpg'),
('Concrete', 'Composite', 'https://example.com/materials/concrete.jpg'),
('Terrazzo', 'Composite', 'https://example.com/materials/terrazzo.jpg'),
('Corian', 'Composite', 'https://example.com/materials/corian.jpg'),
('Recycled Glass', 'Composite', 'https://example.com/materials/recycled-glass.jpg');

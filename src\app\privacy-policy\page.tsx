"use client";
export default function PrivacyPage() {
  return (
    <main className="max-w-3xl mx-auto px-4 py-16">
      <h1 className="text-3xl md:text-5xl font-bold text-white mb-6">Privacy Policy for Room AI</h1>
      <section className="space-y-8 text-gray-300 bg-gray-950 rounded-xl border-2 border-gray-800 px-7 py-7">
        <div>
          <h2 className="font-bold text-xl text-teal-400 mb-2">Information We Collect</h2>
          <span>Name, email, uploaded images, service analytics and behavioral data.</span>
        </div>
        <div>
          <h2 className="font-bold text-xl text-teal-400 mb-2">How We Use Your Information</h2>
          <span>For account operation, analytics, fraud prevention, communications, and system improvement.</span>
        </div>
        <div>
          <h2 className="font-bold text-xl text-teal-400 mb-2">Image Review</h2>
          <span>Images may be reviewed for moderation per the Terms.</span>
        </div>
        <div>
          <h2 className="font-bold text-xl text-teal-400 mb-2">Data Security</h2>
          <span>We take reasonable precautions but cannot guarantee absolute security.</span>
        </div>
        <div>
          <h2 className="font-bold text-xl text-teal-400 mb-2">Contact Information</h2>
          <span>Questions? Contact <EMAIL></span>
        </div>
      </section>
    </main>
  );
}

# Required APIs for RoomAI Clone

## 1. Authentication
- `POST /api/auth/login-email` – Email magic link login
- `POST /api/auth/login-social` – Social oauth (Google, Apple)
- `POST /api/auth/signup` – (if user sign-up distinct)
- `POST /api/auth/logout` – Logout endpoint
- `GET /api/auth/session` – Get current user/session

## 2. Styles
- `GET /api/styles` – Get available style list (with id, name, thumbnail)

## 3. Upload & AI
- `POST /api/upload` – Upload a room image (returns upload info/id or URL)
- `POST /api/ai/redesign` – Trigger AI generation (inputs: upload id URL, selected styles, optional prompts)
- `GET /api/ai/result?id=<job>` – Get AI result (one or more images, possibly status)

## 4. User Data
- `GET /api/user/projects` – List of previous redesigns & images
- `POST /api/user/save` – Save a generated result to user account
- `DELETE /api/user/project/{projectId}` – Delete a redesign / history item

## 5. Billing/Plans
- `GET /api/billing/plans` – List pricing/subscription plans
- `POST /api/billing/subscribe` – Start/upgrade/cancel user subscription
- `GET /api/billing/invoices` – List/view invoices for user
- `POST /api/billing/portal` – Create Stripe portal link for current user

---
**Note:** API endpoints may connect to Supabase, Replicate (or custom AI endpoints), payment providers, and require authentication middleware.

This file is for backend/APIs required for a full RoomAI clone, ready for implementation next.

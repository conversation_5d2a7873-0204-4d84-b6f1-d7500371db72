"use client";
import React, { useState } from 'react';
import MaterialSelector, { SAMPLE_MATERIALS } from '../../components/MaterialSelector';
import { getUniqueMaterialIcon } from '../../components/icons/UniqueMaterialIcons';

// All materials from database schema
const ALL_MATERIALS = [
  // Wood Materials
  { id: 1, name: 'Oak Wood', category: 'Wood' },
  { id: 2, name: 'Walnut Wood', category: 'Wood' },
  { id: 3, name: 'Cherry Wood', category: 'Wood' },
  { id: 4, name: 'Maple Wood', category: '<PERSON>' },
  { id: 5, name: '<PERSON> Wood', category: '<PERSON>' },
  { id: 6, name: 'Pine Wood', category: '<PERSON>' },
  { id: 7, name: 'Ma<PERSON> Wood', category: '<PERSON>' },
  { id: 8, name: 'Teak Wood', category: 'Wood' },
  { id: 9, name: 'Bamboo', category: '<PERSON>' },
  { id: 10, name: 'Reclaimed Wood', category: '<PERSON>' },
  { id: 11, name: 'Driftwood', category: '<PERSON>' },
  { id: 12, name: 'Ebony Wood', category: 'Wood' },

  // Metal Materials
  { id: 13, name: 'Stainless Steel', category: 'Metal' },
  { id: 14, name: 'Chrome', category: 'Metal' },
  { id: 15, name: 'Brass', category: 'Metal' },
  { id: 16, name: 'Copper', category: 'Metal' },
  { id: 17, name: 'Bronze', category: 'Metal' },
  { id: 18, name: 'Iron', category: 'Metal' },
  { id: 19, name: 'Aluminum', category: 'Metal' },
  { id: 20, name: 'Gold Finish', category: 'Metal' },
  { id: 21, name: 'Silver Finish', category: 'Metal' },
  { id: 22, name: 'Pewter', category: 'Metal' },
  { id: 23, name: 'Titanium', category: 'Metal' },
  { id: 24, name: 'Wrought Iron', category: 'Metal' },

  // Stone Materials
  { id: 25, name: 'Marble', category: 'Stone' },
  { id: 26, name: 'Granite', category: 'Stone' },
  { id: 27, name: 'Limestone', category: 'Stone' },
  { id: 28, name: 'Travertine', category: 'Stone' },
  { id: 29, name: 'Slate', category: 'Stone' },
  { id: 30, name: 'Quartzite', category: 'Stone' },
  { id: 31, name: 'Sandstone', category: 'Stone' },
  { id: 32, name: 'Onyx', category: 'Stone' },
  { id: 33, name: 'Basalt', category: 'Stone' },
  { id: 34, name: 'River Rock', category: 'Stone' },
  { id: 35, name: 'Fieldstone', category: 'Stone' },
  { id: 36, name: 'Cobblestone', category: 'Stone' },

  // Fabric Materials
  { id: 37, name: 'Linen', category: 'Fabric' },
  { id: 38, name: 'Cotton', category: 'Fabric' },
  { id: 39, name: 'Velvet', category: 'Fabric' },
  { id: 40, name: 'Silk', category: 'Fabric' },
  { id: 41, name: 'Wool', category: 'Fabric' },
  { id: 42, name: 'Cashmere', category: 'Fabric' },
  { id: 43, name: 'Leather', category: 'Fabric' },
  { id: 44, name: 'Suede', category: 'Fabric' },
  { id: 45, name: 'Chenille', category: 'Fabric' },
  { id: 46, name: 'Tweed', category: 'Fabric' },
  { id: 47, name: 'Burlap', category: 'Fabric' },
  { id: 48, name: 'Canvas', category: 'Fabric' },
  { id: 49, name: 'Denim', category: 'Fabric' },
  { id: 50, name: 'Faux Fur', category: 'Fabric' },

  // Glass Materials
  { id: 51, name: 'Clear Glass', category: 'Glass' },
  { id: 52, name: 'Frosted Glass', category: 'Glass' },
  { id: 53, name: 'Tempered Glass', category: 'Glass' },
  { id: 54, name: 'Stained Glass', category: 'Glass' },
  { id: 55, name: 'Mirrored Glass', category: 'Glass' },
  { id: 56, name: 'Textured Glass', category: 'Glass' },
  { id: 57, name: 'Colored Glass', category: 'Glass' },
  { id: 58, name: 'Laminated Glass', category: 'Glass' },

  // Ceramic Materials
  { id: 59, name: 'Porcelain', category: 'Ceramic' },
  { id: 60, name: 'Ceramic Tile', category: 'Ceramic' },
  { id: 61, name: 'Terra Cotta', category: 'Ceramic' },
  { id: 62, name: 'Stoneware', category: 'Ceramic' },
  { id: 63, name: 'Earthenware', category: 'Ceramic' },
  { id: 64, name: 'Glazed Ceramic', category: 'Ceramic' },

  // Synthetic Materials
  { id: 65, name: 'Acrylic', category: 'Synthetic' },
  { id: 66, name: 'Plastic', category: 'Synthetic' },
  { id: 67, name: 'Vinyl', category: 'Synthetic' },
  { id: 68, name: 'Laminate', category: 'Synthetic' },
  { id: 69, name: 'Fiberglass', category: 'Synthetic' },
  { id: 70, name: 'Carbon Fiber', category: 'Synthetic' },
  { id: 71, name: 'Resin', category: 'Synthetic' },
  { id: 72, name: 'Polyurethane', category: 'Synthetic' },

  // Natural Materials
  { id: 73, name: 'Rattan', category: 'Natural' },
  { id: 74, name: 'Wicker', category: 'Natural' },
  { id: 75, name: 'Jute', category: 'Natural' },
  { id: 76, name: 'Sisal', category: 'Natural' },
  { id: 77, name: 'Cork', category: 'Natural' },
  { id: 78, name: 'Seagrass', category: 'Natural' },
  { id: 79, name: 'Hemp', category: 'Natural' },
  { id: 80, name: 'Cane', category: 'Natural' },
  { id: 81, name: 'Rush', category: 'Natural' },

  // Composite Materials
  { id: 82, name: 'Quartz Composite', category: 'Composite' },
  { id: 83, name: 'Engineered Stone', category: 'Composite' },
  { id: 84, name: 'Concrete', category: 'Composite' },
  { id: 85, name: 'Terrazzo', category: 'Composite' },
  { id: 86, name: 'Corian', category: 'Composite' },
  { id: 87, name: 'Recycled Glass', category: 'Composite' },
];

export default function MaterialsDemoPage() {
  const [selectedMaterials, setSelectedMaterials] = useState<number[]>([]);

  const handleMaterialToggle = (materialId: number) => {
    setSelectedMaterials(prev => 
      prev.includes(materialId)
        ? prev.filter(id => id !== materialId)
        : [...prev, materialId]
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Material Icons Demo
          </h1>
          <p className="text-gray-300 text-lg max-w-2xl mx-auto">
            Interactive showcase of all 87 material icons with custom SVG designs and category-specific colors.
            Each icon is dynamically generated with material-appropriate styling.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-gray-800/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-teal-400">87</div>
            <div className="text-gray-300 text-sm">Total Materials</div>
          </div>
          <div className="bg-gray-800/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-teal-400">9</div>
            <div className="text-gray-300 text-sm">Categories</div>
          </div>
          <div className="bg-gray-800/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-teal-400">{selectedMaterials.length}</div>
            <div className="text-gray-300 text-sm">Selected</div>
          </div>
          <div className="bg-gray-800/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-teal-400">SVG</div>
            <div className="text-gray-300 text-sm">Icon Format</div>
          </div>
        </div>

        {/* Material Selector */}
        <div className="bg-gray-800/30 rounded-2xl p-6 border border-gray-700">
          <MaterialSelector
            materials={ALL_MATERIALS}
            selectedMaterials={selectedMaterials}
            onMaterialToggle={handleMaterialToggle}
            maxSelection={10}
          />
        </div>

        {/* Selected Materials Summary */}
        {selectedMaterials.length > 0 && (
          <div className="mt-8 bg-gray-800/30 rounded-2xl p-6 border border-gray-700">
            <h3 className="text-xl font-semibold text-white mb-4">
              Selected Materials ({selectedMaterials.length})
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {selectedMaterials.map(materialId => {
                const material = ALL_MATERIALS.find(m => m.id === materialId);
                if (!material) return null;
                
                return (
                  <div key={materialId} className="flex flex-col items-center p-3 bg-gray-900 rounded-lg">
                    {getUniqueMaterialIcon(material.name)}
                    <span className="text-xs text-white mt-2 text-center">{material.name}</span>
                    <span className="text-xs text-gray-400">{material.category}</span>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Technical Info */}
        <div className="mt-8 bg-gray-800/30 rounded-2xl p-6 border border-gray-700">
          <h3 className="text-xl font-semibold text-white mb-4">Technical Features</h3>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-lg font-medium text-teal-400 mb-2">Icon Features</h4>
              <ul className="text-gray-300 space-y-1 text-sm">
                <li>• Custom SVG designs for each material category</li>
                <li>• Material-specific color mapping</li>
                <li>• Scalable vector graphics (any size)</li>
                <li>• Consistent visual style across categories</li>
                <li>• Optimized for web performance</li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-medium text-teal-400 mb-2">Interactive Features</h4>
              <ul className="text-gray-300 space-y-1 text-sm">
                <li>• Category-based filtering</li>
                <li>• Multi-selection with limits</li>
                <li>• Visual selection indicators</li>
                <li>• Hover and active states</li>
                <li>• Responsive grid layout</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

"use client";

const FAQS = [
  {
    q: "Can I use these designs commercially?",
    a: "Certainly! If you're a user with a paid subscription that supports commercial usage, you have the rights to use the designs for commercial purposes. But remember, rights for personal and commercial use depend on the type of subscription you hold. Always ensure your usage aligns with your subscription terms."
  },
  {
    q: "What type of input should I provide to get good results?",
    a: "Whether you want to restyle an existing space or create a design from scratch, our AI can handle it. You can upload images, select design elements or even create from scratch - the more detail you provide, the better our AI can meet your expectations."
  },
  {
    q: "How does Room AI differ from other interior design tools?",
    a: "Since our launch in September 2022, several competitors have emerged, applying similar AI technology in predictable ways. Room AI goes a step further by collaborating with users and industry experts. As a result, we offer more than just powerful AI technology; we deliver it through an easy-to-use interface, packed with a variety of customization options and suggestions such as color palettes and materials, ensuring that we effectively cater to your needs."
  },
  {
    q: "Do I still need an interior designer?",
    a: "Room AI is a powerful tool that could serve as a complete solution for many users. However, depending on your specific needs, the expertise of an interior designer can provide additional value. We see Room AI working in harmony with professional designers, augmenting their skills with our AI technology."
  },
  {
    q: "Is payment secure?",
    a: "Absolutely. We use Stripe, a leading global payment processor, to ensure your transactions are secure. Stripe securely stores your credit card details in compliance with data protection regulations."
  },
  {
    q: "How do you handle my data and respect my privacy?",
    a: "We take your privacy seriously and are committed to keeping your uploads confidential. We assure you that we will not publish any outputs of your account on our homepage or anywhere else without your explicit permission. We follow stringent data management practices to protect your data."
  },
  {
    q: "Who created Room AI?",
    a: "I, Marc Khlbrugge, developed Room AI with the help of input from industry professionals. Through extensive use of AI technology while developing Room AI, I'm able to offer Room AI's services at an affordable price while ensuring a sustainable business."
  },
];

export default function FAQPage() {
  return (
    <main className="max-w-3xl mx-auto px-4 py-16">
      <h1 className="text-3xl md:text-5xl font-bold text-white mb-10">Your questions, answered</h1>
      <section className="space-y-4">
        {FAQS.map((faq, i) => (
          <div key={i} className="rounded-xl border-2 border-gray-800 bg-gray-950 px-6 py-5">
            <div className="font-bold text-lg text-teal-400 mb-2">{faq.q}</div>
            <div className="text-gray-300 text-base whitespace-pre-line">{faq.a}</div>
          </div>
        ))}
      </section>
    </main>
  );
}

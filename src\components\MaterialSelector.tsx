"use client";
import React, { useState } from 'react';
import { getUniqueMaterialIcon } from './icons/UniqueMaterialIcons';

interface Material {
  id: number;
  name: string;
  category: string;
  preview_image?: string;
}

interface MaterialSelectorProps {
  materials: Material[];
  selectedMaterials: number[];
  onMaterialToggle: (materialId: number) => void;
  maxSelection?: number;
}

const MATERIAL_CATEGORIES = [
  'All',
  'Wood',
  'Metal', 
  'Stone',
  'Fabric',
  'Glass',
  'Ceramic',
  'Synthetic',
  'Natural',
  'Composite'
];

export const MaterialSelector: React.FC<MaterialSelectorProps> = ({
  materials,
  selectedMaterials,
  onMaterialToggle,
  maxSelection = 5
}) => {
  const [selectedCategory, setSelectedCategory] = useState('All');

  const filteredMaterials = selectedCategory === 'All' 
    ? materials 
    : materials.filter(material => material.category === selectedCategory);

  const isSelected = (materialId: number) => selectedMaterials.includes(materialId);
  const canSelect = selectedMaterials.length < maxSelection;

  const handleMaterialClick = (materialId: number) => {
    if (isSelected(materialId) || canSelect) {
      onMaterialToggle(materialId);
    }
  };

  return (
    <div className="w-full">
      {/* Category Filter */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-white mb-3">Material Categories</h3>
        <div className="flex flex-wrap gap-2">
          {MATERIAL_CATEGORIES.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                selectedCategory === category
                  ? 'bg-teal-600 text-white border-teal-400'
                  : 'bg-gray-800 text-gray-300 border-gray-700 hover:bg-gray-700'
              } border`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* Selection Counter */}
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-white">
          Select Materials
          {selectedCategory !== 'All' && (
            <span className="text-sm font-normal text-gray-400 ml-2">
              ({selectedCategory})
            </span>
          )}
        </h3>
        <div className="text-sm text-teal-400 font-medium">
          {selectedMaterials.length}/{maxSelection} selected
        </div>
      </div>

      {/* Materials Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        {filteredMaterials.map((material) => {
          const selected = isSelected(material.id);
          const disabled = !selected && !canSelect;

          return (
            <button
              key={material.id}
              onClick={() => handleMaterialClick(material.id)}
              disabled={disabled}
              className={`
                group flex flex-col items-center p-3 rounded-xl border-2 transition-all duration-200
                ${selected 
                  ? 'border-teal-500 bg-teal-500/10 ring-2 ring-teal-300/40' 
                  : disabled
                    ? 'border-gray-700 bg-gray-900/50 opacity-50 cursor-not-allowed'
                    : 'border-gray-700 bg-gray-900 hover:border-teal-500 hover:bg-gray-800 active:scale-95'
                }
              `}
              title={`${material.name} (${material.category})`}
            >
              {/* Material Icon */}
              <div className={`mb-2 transition-transform duration-200 ${
                selected ? 'scale-110' : 'group-hover:scale-105'
              }`}>
                {getUniqueMaterialIcon(material.name)}
              </div>

              {/* Material Name */}
              <span className={`text-xs font-medium text-center leading-tight ${
                selected ? 'text-teal-400' : 'text-white'
              }`}>
                {material.name}
              </span>

              {/* Category Badge */}
              <span className={`text-xs mt-1 px-2 py-0.5 rounded-full ${
                selected 
                  ? 'bg-teal-500/20 text-teal-300' 
                  : 'bg-gray-700 text-gray-400'
              }`}>
                {material.category}
              </span>

              {/* Selection Indicator */}
              {selected && (
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-teal-500 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* No Materials Message */}
      {filteredMaterials.length === 0 && (
        <div className="text-center py-8">
          <div className="text-gray-400 text-lg mb-2">No materials found</div>
          <div className="text-gray-500 text-sm">
            Try selecting a different category
          </div>
        </div>
      )}

      {/* Selection Limit Warning */}
      {selectedMaterials.length >= maxSelection && (
        <div className="mt-4 p-3 bg-amber-500/10 border border-amber-500/30 rounded-lg">
          <div className="text-amber-400 text-sm font-medium">
            Maximum materials selected ({maxSelection})
          </div>
          <div className="text-amber-300 text-xs mt-1">
            Deselect a material to choose a different one
          </div>
        </div>
      )}
    </div>
  );
};

// Sample materials data for testing
export const SAMPLE_MATERIALS: Material[] = [
  // Wood
  { id: 1, name: 'Oak Wood', category: 'Wood' },
  { id: 2, name: 'Walnut Wood', category: 'Wood' },
  { id: 3, name: 'Cherry Wood', category: 'Wood' },
  { id: 4, name: 'Maple Wood', category: 'Wood' },
  
  // Metal
  { id: 5, name: 'Stainless Steel', category: 'Metal' },
  { id: 6, name: 'Chrome', category: 'Metal' },
  { id: 7, name: 'Brass', category: 'Metal' },
  { id: 8, name: 'Copper', category: 'Metal' },
  
  // Stone
  { id: 9, name: 'Marble', category: 'Stone' },
  { id: 10, name: 'Granite', category: 'Stone' },
  { id: 11, name: 'Limestone', category: 'Stone' },
  { id: 12, name: 'Slate', category: 'Stone' },
  
  // Fabric
  { id: 13, name: 'Linen', category: 'Fabric' },
  { id: 14, name: 'Cotton', category: 'Fabric' },
  { id: 15, name: 'Velvet', category: 'Fabric' },
  { id: 16, name: 'Silk', category: 'Fabric' },
  
  // Glass
  { id: 17, name: 'Clear Glass', category: 'Glass' },
  { id: 18, name: 'Frosted Glass', category: 'Glass' },
  { id: 19, name: 'Tempered Glass', category: 'Glass' },
  { id: 20, name: 'Stained Glass', category: 'Glass' },
];

export default MaterialSelector;

# 🏠 EcoDesignAI: Complete Development Plan
## Skip Validation - Build Fast, Launch Faster

> **Market Data Says It All**: 8,500 monthly searches, competitors at $20K+ MRR, 1700% growth surge. Time to build, not validate!

---

## 🎯 **DEVELOPMENT STRATEGY: 4 PHASES TO $10K MRR**

### **Phase 1: Foundation & Core MVP** (Weeks 1-4)
**Goal**: Build working AI interior design tool with basic features
**Timeline**: 4 weeks
**Budget**: $0 (free tiers only)

### **Phase 2: Monetization & Growth** (Weeks 5-8) 
**Goal**: Add payments, affiliate system, scale to 1,000 users
**Timeline**: 4 weeks  
**Budget**: $100/month (paid tiers)

### **Phase 3: Advanced Features & Scale** (Weeks 9-16)
**Goal**: Premium features, team collaboration, 5,000 users
**Timeline**: 8 weeks
**Budget**: $300/month

### **Phase 4: Market Domination** (Weeks 17-24)
**Goal**: $10K MRR, mobile app, enterprise features
**Timeline**: 8 weeks
**Budget**: $500/month

---

# 🚀 **PHASE 1: FOUNDATION & CORE MVP** (Weeks 1-4)

## **Week 1: Project Setup & Infrastructure**

### **Sub-Phase 1A: Development Environment** (Days 1-2)
**What we're building**: Setting up all the tools and accounts needed

**Like explaining to a 5-year-old**: 
*"Before we can build our awesome room design app, we need to get all our building tools ready - like getting crayons, paper, and a desk before drawing!"*

**Step-by-step tasks**:

**Day 1 Morning: Account Setup**
```bash
# 1. Create accounts (30 minutes each)
- GitHub account (for code storage)
- Vercel account (for website hosting) 
- Supabase account (for database)
- Hugging Face account (for AI models)
- PostHog account (for analytics)
```

**Day 1 Afternoon: Local Development**
```bash
# 2. Install development tools
npm install -g create-next-app
npm install -g supabase

# 3. Create project structure
npx create-next-app@latest ecodesignai --typescript --tailwind --eslint
cd ecodesignai
```

**Day 2: Project Configuration**
```bash
# 4. Install required packages
npm install @supabase/supabase-js
npm install @supabase/auth-ui-react
npm install lucide-react
npm install posthog-js
npm install react-dropzone
npm install sharp
```

**Deliverable**: Working development environment with all tools connected

### **Sub-Phase 1B: Database Design** (Days 3-4)
**What we're building**: The brain that remembers everything about users and designs

**Like explaining to a 5-year-old**:
*"This is like creating filing cabinets where we keep everyone's information - one drawer for users, one for their room pictures, one for furniture!"*

**Database Schema Setup**:
```sql
-- Users table (Supabase handles this automatically)

-- Designs table - stores all room transformations
CREATE TABLE designs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  original_image_url TEXT NOT NULL,
  generated_image_url TEXT,
  style TEXT NOT NULL,
  room_type TEXT NOT NULL,
  status TEXT DEFAULT 'processing',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Furniture table - all the eco-friendly furniture
CREATE TABLE furniture (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  category TEXT NOT NULL, -- chair, table, sofa, etc.
  image_url TEXT NOT NULL,
  partner_url TEXT NOT NULL,
  price_range TEXT, -- $100-200, $200-500, etc.
  carbon_footprint INTEGER, -- kg of CO2
  recyclability_percentage INTEGER, -- 0-100%
  style_tags TEXT[], -- modern, scandinavian, etc.
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User subscriptions
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) UNIQUE,
  plan TEXT DEFAULT 'free', -- free, personal, pro, business
  renders_used INTEGER DEFAULT 0,
  renders_limit INTEGER DEFAULT 3,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Deliverable**: Database with all tables created and ready

### **Sub-Phase 1C: Basic UI Framework** (Days 5-7)
**What we're building**: The pretty interface users will see and click

**Like explaining to a 5-year-old**:
*"Now we're painting the walls and putting furniture in our app house - making it look pretty and easy to use!"*

**Core Components**:

**1. Layout Component** (`components/Layout.tsx`):
```typescript
import { ReactNode } from 'react';
import { Leaf, Upload, Palette, Users } from 'lucide-react';

interface LayoutProps {
  children: ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Navigation Bar */}
      <nav className="bg-white shadow-sm border-b border-green-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Leaf className="h-8 w-8 text-green-600" />
              <span className="ml-2 text-xl font-bold text-gray-900">
                EcoDesignAI
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <button className="text-gray-600 hover:text-green-600">
                Sign In
              </button>
              <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                Get Started
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main>{children}</main>

      {/* Footer */}
      <footer className="bg-gray-50 border-t border-gray-200 mt-20">
        <div className="max-w-7xl mx-auto py-8 px-4">
          <p className="text-center text-gray-600">
            © 2025 EcoDesignAI - Sustainable Interior Design with AI
          </p>
        </div>
      </footer>
    </div>
  );
}
```

**2. Home Page** (`pages/index.tsx`):
```typescript
import Layout from '../components/Layout';
import { Upload, Sparkles, ShoppingBag, Leaf } from 'lucide-react';

export default function Home() {
  return (
    <Layout>
      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-4 py-20">
        <div className="text-center">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            Transform Your Room with 
            <span className="text-green-600"> Sustainable AI Design</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Upload a photo of your room and get instant AI-powered designs 
            featuring eco-friendly furniture you can buy with one click.
          </p>
          <button className="bg-green-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-green-700 transition-colors">
            Start Designing Now - Free!
          </button>
        </div>

        {/* How It Works */}
        <div className="mt-20 grid md:grid-cols-3 gap-8">
          <div className="text-center">
            <Upload className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">1. Upload Your Room</h3>
            <p className="text-gray-600">Take a photo of any room in your home</p>
          </div>
          <div className="text-center">
            <Sparkles className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">2. AI Magic Happens</h3>
            <p className="text-gray-600">Our AI creates beautiful sustainable designs</p>
          </div>
          <div className="text-center">
            <ShoppingBag className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">3. Shop Eco-Furniture</h3>
            <p className="text-gray-600">Click on furniture to buy eco-friendly pieces</p>
          </div>
        </div>
      </div>
    </Layout>
  );
}
```

**Deliverable**: Beautiful, responsive website that looks professional

## **Week 2: AI Integration & Core Features**

### **Sub-Phase 2A: AI Model Setup** (Days 8-10)
**What we're building**: The smart brain that transforms room photos

**Like explaining to a 5-year-old**:
*"We're teaching our app to be an artist! We give it a picture of a room, and it learns to make it look different and prettier!"*

**Hugging Face Integration**:

**1. API Service** (`lib/ai-service.ts`):
```typescript
interface AIResponse {
  image_url: string;
  confidence: number;
}

export class AIDesignService {
  private apiKey: string;
  
  constructor() {
    this.apiKey = process.env.HUGGING_FACE_API_KEY!;
  }

  async transformRoom(
    imageFile: File, 
    style: string, 
    roomType: string
  ): Promise<AIResponse> {
    // Convert image to base64
    const imageBase64 = await this.fileToBase64(imageFile);
    
    // Call Hugging Face API
    const response = await fetch(
      'https://api-inference.huggingface.co/models/BertChristiaens/controlnet-seg-room',
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: imageBase64,
          parameters: {
            prompt: `${style} ${roomType} interior design, sustainable furniture, eco-friendly, natural lighting, clean aesthetic`,
            num_inference_steps: 20,
            guidance_scale: 7.5,
            strength: 0.8
          }
        })
      }
    );

    if (!response.ok) {
      throw new Error('AI transformation failed');
    }

    const result = await response.blob();
    const imageUrl = await this.uploadToSupabase(result);
    
    return {
      image_url: imageUrl,
      confidence: 0.9
    };
  }

  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  }

  private async uploadToSupabase(blob: Blob): Promise<string> {
    // Upload generated image to Supabase storage
    const fileName = `generated-${Date.now()}.jpg`;
    const { data, error } = await supabase.storage
      .from('designs')
      .upload(fileName, blob);
    
    if (error) throw error;
    
    const { data: { publicUrl } } = supabase.storage
      .from('designs')
      .getPublicUrl(fileName);
    
    return publicUrl;
  }
}
```

**Deliverable**: Working AI that can transform room photos

### **Sub-Phase 2B: Upload & Design Interface** (Days 11-14)
**What we're building**: The main page where users upload photos and see magic happen

**Like explaining to a 5-year-old**:
*"This is like a magic photo booth! You put in a picture of your room, choose what style you want, and it gives you back a beautiful new room!"*

**Design Studio Component** (`components/DesignStudio.tsx`):
```typescript
import { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, Loader, Download } from 'lucide-react';
import { AIDesignService } from '../lib/ai-service';

export default function DesignStudio() {
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedStyle, setSelectedStyle] = useState('modern');
  const [selectedRoom, setSelectedRoom] = useState('living-room');

  const aiService = new AIDesignService();

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png']
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      setUploadedImage(acceptedFiles[0]);
    }
  });

  const handleGenerate = async () => {
    if (!uploadedImage) return;
    
    setIsProcessing(true);
    try {
      const result = await aiService.transformRoom(
        uploadedImage, 
        selectedStyle, 
        selectedRoom
      );
      setGeneratedImage(result.image_url);
    } catch (error) {
      console.error('Generation failed:', error);
      alert('Something went wrong. Please try again!');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h1 className="text-3xl font-bold text-center mb-8 text-gray-900">
        🏠 Transform Your Room with AI
      </h1>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Upload Section */}
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">1. Upload Your Room Photo</h2>
          
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive 
                ? 'border-green-500 bg-green-50' 
                : 'border-gray-300 hover:border-green-400'
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            {uploadedImage ? (
              <div>
                <p className="text-green-600 font-semibold">
                  ✅ {uploadedImage.name}
                </p>
                <img 
                  src={URL.createObjectURL(uploadedImage)}
                  alt="Uploaded room"
                  className="mt-4 max-h-48 mx-auto rounded-lg"
                />
              </div>
            ) : (
              <div>
                <p className="text-lg mb-2">Drop your room photo here</p>
                <p className="text-gray-500">or click to browse</p>
              </div>
            )}
          </div>

          {/* Style Selection */}
          <div>
            <h3 className="text-lg font-semibold mb-3">2. Choose Your Style</h3>
            <div className="grid grid-cols-2 gap-3">
              {[
                { id: 'modern', name: 'Modern Eco', emoji: '🏢' },
                { id: 'scandinavian', name: 'Scandinavian', emoji: '🌲' },
                { id: 'minimalist', name: 'Minimalist', emoji: '⚪' },
                { id: 'bohemian', name: 'Boho Eco', emoji: '🌿' }
              ].map(style => (
                <button
                  key={style.id}
                  onClick={() => setSelectedStyle(style.id)}
                  className={`p-3 rounded-lg border-2 transition-colors ${
                    selectedStyle === style.id
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-green-300'
                  }`}
                >
                  <div className="text-2xl mb-1">{style.emoji}</div>
                  <div className="font-medium">{style.name}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Room Type */}
          <div>
            <h3 className="text-lg font-semibold mb-3">3. Room Type</h3>
            <select
              value={selectedRoom}
              onChange={(e) => setSelectedRoom(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            >
              <option value="living-room">Living Room</option>
              <option value="bedroom">Bedroom</option>
              <option value="kitchen">Kitchen</option>
              <option value="bathroom">Bathroom</option>
              <option value="office">Home Office</option>
            </select>
          </div>

          {/* Generate Button */}
          <button
            onClick={handleGenerate}
            disabled={!uploadedImage || isProcessing}
            className="w-full bg-green-600 text-white py-4 rounded-lg font-semibold text-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {isProcessing ? (
              <div className="flex items-center justify-center">
                <Loader className="animate-spin h-5 w-5 mr-2" />
                Creating Your Design...
              </div>
            ) : (
              '✨ Generate Eco Design'
            )}
          </button>
        </div>

        {/* Results Section */}
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">4. Your Sustainable Design</h2>
          
          {generatedImage ? (
            <div className="space-y-4">
              <div className="relative">
                <img 
                  src={generatedImage}
                  alt="Generated design"
                  className="w-full rounded-lg shadow-lg"
                />
                <button className="absolute top-4 right-4 bg-white p-2 rounded-full shadow-lg hover:bg-gray-50">
                  <Download className="h-5 w-5" />
                </button>
              </div>
              
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-semibold text-green-800 mb-2">
                  🌱 Sustainability Impact
                </h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Carbon Saved:</span>
                    <span className="font-semibold ml-2">15kg CO₂</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Recyclable:</span>
                    <span className="font-semibold ml-2">86%</span>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-12 text-center">
              <Sparkles className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">
                Your beautiful eco-design will appear here
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
```

**Deliverable**: Complete design studio where users can upload and transform rooms

**End of Week 2**: Users can upload room photos and get AI-generated sustainable designs!

## **Week 3: Furniture Integration & Shopping**

### **Sub-Phase 3A: Furniture Database Setup** (Days 15-17)
**What we're building**: A catalog of eco-friendly furniture that users can click and buy

**Like explaining to a 5-year-old**:
*"We're filling up our toy store! We need to put all the eco-friendly chairs, tables, and sofas on the shelves so people can see them and buy them!"*

**Furniture Data Structure**:
```sql
-- Insert sample eco-friendly furniture
INSERT INTO furniture (name, category, image_url, partner_url, price_range, carbon_footprint, recyclability_percentage, style_tags) VALUES
('Bamboo Dining Chair', 'chair', 'https://example.com/bamboo-chair.jpg', 'https://partner.com/bamboo-chair', '$100-200', 5, 95, ARRAY['modern', 'scandinavian']),
('Recycled Wood Coffee Table', 'table', 'https://example.com/wood-table.jpg', 'https://partner.com/wood-table', '$200-400', 8, 90, ARRAY['modern', 'minimalist']),
('Organic Cotton Sofa', 'sofa', 'https://example.com/cotton-sofa.jpg', 'https://partner.com/cotton-sofa', '$800-1200', 12, 85, ARRAY['scandinavian', 'bohemian']),
('Cork Floor Lamp', 'lighting', 'https://example.com/cork-lamp.jpg', 'https://partner.com/cork-lamp', '$50-100', 3, 100, ARRAY['modern', 'bohemian']);
```

**Furniture Service** (`lib/furniture-service.ts`):
```typescript
export interface Furniture {
  id: string;
  name: string;
  category: string;
  image_url: string;
  partner_url: string;
  price_range: string;
  carbon_footprint: number;
  recyclability_percentage: number;
  style_tags: string[];
}

export class FurnitureService {
  async getFurnitureByStyle(style: string): Promise<Furniture[]> {
    const { data, error } = await supabase
      .from('furniture')
      .select('*')
      .contains('style_tags', [style])
      .order('carbon_footprint', { ascending: true }); // Show most eco-friendly first

    if (error) throw error;
    return data;
  }

  async trackClick(furnitureId: string, userId?: string) {
    // Track furniture clicks for analytics
    posthog.capture('furniture_click', {
      furniture_id: furnitureId,
      user_id: userId,
      timestamp: new Date().toISOString()
    });
  }
}
```

**Deliverable**: Database filled with 20+ eco-friendly furniture items

### **Sub-Phase 3B: Clickable Furniture Overlay** (Days 18-21)
**What we're building**: Magic clickable spots on the AI-generated images

**Like explaining to a 5-year-old**:
*"We're putting invisible buttons on the furniture in the pictures! When someone clicks on a chair in the picture, it opens a store where they can buy that exact chair!"*

**Furniture Overlay Component** (`components/FurnitureOverlay.tsx`):
```typescript
import { useState, useEffect } from 'react';
import { ShoppingBag, Leaf, Info } from 'lucide-react';
import { FurnitureService, Furniture } from '../lib/furniture-service';

interface FurnitureOverlayProps {
  imageUrl: string;
  style: string;
  onFurnitureClick: (furniture: Furniture) => void;
}

export default function FurnitureOverlay({ imageUrl, style, onFurnitureClick }: FurnitureOverlayProps) {
  const [furniture, setFurniture] = useState<Furniture[]>([]);
  const [showHotspots, setShowHotspots] = useState(true);
  const furnitureService = new FurnitureService();

  useEffect(() => {
    loadFurniture();
  }, [style]);

  const loadFurniture = async () => {
    try {
      const items = await furnitureService.getFurnitureByStyle(style);
      setFurniture(items.slice(0, 5)); // Show top 5 most eco-friendly
    } catch (error) {
      console.error('Failed to load furniture:', error);
    }
  };

  const handleFurnitureClick = async (item: Furniture) => {
    await furnitureService.trackClick(item.id);
    onFurnitureClick(item);

    // Open partner link in new tab
    window.open(item.partner_url, '_blank');
  };

  // Predefined hotspot positions for different furniture types
  const getHotspotPosition = (category: string, index: number) => {
    const positions = {
      chair: [
        { top: '60%', left: '25%' },
        { top: '65%', left: '70%' }
      ],
      table: [
        { top: '70%', left: '45%' }
      ],
      sofa: [
        { top: '55%', left: '50%' }
      ],
      lighting: [
        { top: '30%', left: '20%' },
        { top: '25%', left: '80%' }
      ]
    };

    return positions[category]?.[index] || { top: '50%', left: '50%' };
  };

  return (
    <div className="relative">
      <img
        src={imageUrl}
        alt="Generated room design"
        className="w-full rounded-lg shadow-lg"
      />

      {/* Toggle Hotspots Button */}
      <button
        onClick={() => setShowHotspots(!showHotspots)}
        className="absolute top-4 left-4 bg-white p-2 rounded-full shadow-lg hover:bg-gray-50"
      >
        <ShoppingBag className={`h-5 w-5 ${showHotspots ? 'text-green-600' : 'text-gray-400'}`} />
      </button>

      {/* Furniture Hotspots */}
      {showHotspots && furniture.map((item, index) => {
        const position = getHotspotPosition(item.category, index);
        return (
          <div
            key={item.id}
            className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group"
            style={{ top: position.top, left: position.left }}
            onClick={() => handleFurnitureClick(item)}
          >
            {/* Pulsing Dot */}
            <div className="relative">
              <div className="w-6 h-6 bg-green-500 rounded-full animate-pulse shadow-lg"></div>
              <div className="absolute inset-0 w-6 h-6 bg-green-500 rounded-full animate-ping opacity-75"></div>
            </div>

            {/* Hover Card */}
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-white p-3 rounded-lg shadow-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200 w-48 z-10">
              <img
                src={item.image_url}
                alt={item.name}
                className="w-full h-24 object-cover rounded mb-2"
              />
              <h4 className="font-semibold text-sm">{item.name}</h4>
              <p className="text-xs text-gray-600 mb-2">{item.price_range}</p>

              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center text-green-600">
                  <Leaf className="h-3 w-3 mr-1" />
                  {item.carbon_footprint}kg CO₂
                </div>
                <div className="text-blue-600">
                  {item.recyclability_percentage}% recyclable
                </div>
              </div>

              <button className="w-full mt-2 bg-green-600 text-white py-1 rounded text-xs hover:bg-green-700">
                Shop Now
              </button>
            </div>
          </div>
        );
      })}

      {/* Furniture Panel */}
      <div className="absolute bottom-4 left-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-4">
        <h3 className="font-semibold mb-3 flex items-center">
          <Leaf className="h-4 w-4 text-green-600 mr-2" />
          Eco-Friendly Furniture in This Design
        </h3>
        <div className="grid grid-cols-5 gap-2">
          {furniture.map(item => (
            <button
              key={item.id}
              onClick={() => handleFurnitureClick(item)}
              className="group relative"
            >
              <img
                src={item.image_url}
                alt={item.name}
                className="w-full h-16 object-cover rounded border-2 border-transparent group-hover:border-green-500 transition-colors"
              />
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded transition-colors"></div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
```

**Deliverable**: Interactive images with clickable furniture that opens shopping links

## **Week 4: User Authentication & Basic Analytics**

### **Sub-Phase 4A: User Authentication** (Days 22-24)
**What we're building**: Login system so users can save their designs

**Like explaining to a 5-year-old**:
*"We're making a special key for each person! When they come back to our app, they use their key to see all their room designs they made before!"*

**Auth Setup** (`lib/auth.ts`):
```typescript
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export class AuthService {
  async signUp(email: string, password: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    });

    if (error) throw error;

    // Create user subscription record
    if (data.user) {
      await supabase.from('subscriptions').insert({
        user_id: data.user.id,
        plan: 'free',
        renders_limit: 3
      });
    }

    return data;
  }

  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;
    return data;
  }

  async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  }

  async getCurrentUser() {
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  }

  async getUserSubscription(userId: string) {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) throw error;
    return data;
  }
}
```

**Login Component** (`components/AuthModal.tsx`):
```typescript
import { useState } from 'react';
import { X, Mail, Lock, User } from 'lucide-react';
import { AuthService } from '../lib/auth';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export default function AuthModal({ isOpen, onClose, onSuccess }: AuthModalProps) {
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const authService = new AuthService();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (isLogin) {
        await authService.signIn(email, password);
      } else {
        await authService.signUp(email, password);
      }
      onSuccess();
      onClose();
    } catch (error) {
      alert(error.message);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">
            {isLogin ? 'Welcome Back!' : 'Join EcoDesignAI'}
          </h2>
          <button onClick={onClose}>
            <X className="h-6 w-6 text-gray-400" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="<EMAIL>"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="••••••••"
                required
              />
            </div>
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-green-600 text-white py-2 rounded-lg font-semibold hover:bg-green-700 disabled:bg-gray-400"
          >
            {loading ? 'Loading...' : (isLogin ? 'Sign In' : 'Create Account')}
          </button>
        </form>

        <div className="mt-4 text-center">
          <button
            onClick={() => setIsLogin(!isLogin)}
            className="text-green-600 hover:text-green-700"
          >
            {isLogin ? "Don't have an account? Sign up" : "Already have an account? Sign in"}
          </button>
        </div>

        {!isLogin && (
          <div className="mt-4 p-3 bg-green-50 rounded-lg">
            <p className="text-sm text-green-800">
              🎉 <strong>Free Account Includes:</strong>
              <br />• 3 AI room designs per month
              <br />• Access to eco-furniture catalog
              <br />• Sustainability impact tracking
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
```

**Deliverable**: Complete user authentication system with free account limits

### **Sub-Phase 4B: Analytics & Usage Tracking** (Days 25-28)
**What we're building**: System to track what users do so we can improve

**Like explaining to a 5-year-old**:
*"We're like detectives! We watch (in a good way) what people like to do in our app so we can make it even better for them!"*

**Analytics Service** (`lib/analytics.ts`):
```typescript
import posthog from 'posthog-js';

export class AnalyticsService {
  constructor() {
    if (typeof window !== 'undefined') {
      posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY!, {
        api_host: 'https://app.posthog.com'
      });
    }
  }

  // Track user actions
  trackDesignGenerated(style: string, roomType: string, userId?: string) {
    posthog.capture('design_generated', {
      style,
      room_type: roomType,
      user_id: userId,
      timestamp: new Date().toISOString()
    });
  }

  trackFurnitureClick(furnitureId: string, furnitureName: string, userId?: string) {
    posthog.capture('furniture_clicked', {
      furniture_id: furnitureId,
      furniture_name: furnitureName,
      user_id: userId,
      timestamp: new Date().toISOString()
    });
  }

  trackUserSignup(userId: string) {
    posthog.capture('user_signup', {
      user_id: userId,
      timestamp: new Date().toISOString()
    });

    posthog.identify(userId);
  }

  trackUpgrade(userId: string, fromPlan: string, toPlan: string) {
    posthog.capture('plan_upgrade', {
      user_id: userId,
      from_plan: fromPlan,
      to_plan: toPlan,
      timestamp: new Date().toISOString()
    });
  }

  // Track page views
  trackPageView(page: string) {
    posthog.capture('$pageview', {
      $current_url: window.location.href,
      page_name: page
    });
  }
}
```

**Usage Dashboard Component** (`components/UsageDashboard.tsx`):
```typescript
import { useState, useEffect } from 'react';
import { BarChart3, Zap, Leaf, TrendingUp } from 'lucide-react';

interface UsageStats {
  designsThisMonth: number;
  designsRemaining: number;
  furnitureClicks: number;
  carbonSaved: number;
}

export default function UsageDashboard({ userId }: { userId: string }) {
  const [stats, setStats] = useState<UsageStats | null>(null);

  useEffect(() => {
    loadUsageStats();
  }, [userId]);

  const loadUsageStats = async () => {
    // Get user's designs this month
    const { data: designs } = await supabase
      .from('designs')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString());

    // Get user's subscription
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    setStats({
      designsThisMonth: designs?.length || 0,
      designsRemaining: (subscription?.renders_limit || 3) - (subscription?.renders_used || 0),
      furnitureClicks: 12, // This would come from analytics
      carbonSaved: (designs?.length || 0) * 15 // 15kg CO2 per design
    });
  };

  if (!stats) return <div>Loading...</div>;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold mb-4 flex items-center">
        <BarChart3 className="h-5 w-5 text-green-600 mr-2" />
        Your Impact This Month
      </h3>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center p-4 bg-blue-50 rounded-lg">
          <Zap className="h-8 w-8 text-blue-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-blue-600">{stats.designsThisMonth}</div>
          <div className="text-sm text-gray-600">Designs Created</div>
        </div>

        <div className="text-center p-4 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">{stats.designsRemaining}</div>
          <div className="text-sm text-gray-600">Designs Remaining</div>
        </div>

        <div className="text-center p-4 bg-purple-50 rounded-lg">
          <TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-purple-600">{stats.furnitureClicks}</div>
          <div className="text-sm text-gray-600">Furniture Viewed</div>
        </div>

        <div className="text-center p-4 bg-green-50 rounded-lg">
          <Leaf className="h-8 w-8 text-green-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-green-600">{stats.carbonSaved}kg</div>
          <div className="text-sm text-gray-600">CO₂ Saved</div>
        </div>
      </div>

      {stats.designsRemaining <= 1 && (
        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800">
            ⚠️ You're almost out of free designs!
            <button className="ml-2 text-yellow-900 underline font-semibold">
              Upgrade to Personal ($19/month)
            </button>
          </p>
        </div>
      )}
    </div>
  );
}
```

**Deliverable**: Complete analytics system tracking user behavior and usage limits

**🎉 END OF PHASE 1**: We now have a working AI interior design app with:
- ✅ Beautiful UI that works on all devices
- ✅ AI that transforms room photos into sustainable designs
- ✅ Clickable eco-friendly furniture with shopping links
- ✅ User accounts with free tier limits
- ✅ Analytics tracking everything users do

**Phase 1 Success Metrics**:
- Working MVP deployed to production
- Users can create 3 free designs per month
- 20+ eco-friendly furniture items in database
- All user actions tracked for optimization

---

# 💰 **PHASE 2: MONETIZATION & GROWTH** (Weeks 5-8)

## **Week 5: Payment System & Subscription Plans**

### **Sub-Phase 5A: Stripe Integration** (Days 29-31)
**What we're building**: The cash register that lets people pay for premium features

**Like explaining to a 5-year-old**:
*"We're building a magical piggy bank! When people want more room designs or special features, they can put money in our piggy bank and get cool new powers!"*

**Stripe Setup** (`lib/stripe.ts`):
```typescript
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  renders: number;
  features: string[];
  stripePriceId: string;
}

export const PLANS: SubscriptionPlan[] = [
  {
    id: 'free',
    name: 'Free',
    price: 0,
    renders: 3,
    features: ['3 designs per month', 'Basic furniture catalog', 'Watermarked exports'],
    stripePriceId: ''
  },
  {
    id: 'personal',
    name: 'Personal',
    price: 19,
    renders: -1, // unlimited
    features: ['Unlimited designs', 'HD exports', 'Priority support', 'Advanced sustainability metrics'],
    stripePriceId: 'price_personal_monthly'
  },
  {
    id: 'pro',
    name: 'Pro',
    price: 49,
    renders: -1,
    features: ['Everything in Personal', 'Team collaboration', 'Custom styles', 'API access'],
    stripePriceId: 'price_pro_monthly'
  },
  {
    id: 'business',
    name: 'Business',
    price: 199,
    renders: -1,
    features: ['Everything in Pro', 'White-label options', 'Priority AI processing', 'Dedicated support'],
    stripePriceId: 'price_business_monthly'
  }
];

export class PaymentService {
  async createCheckoutSession(planId: string, userId: string) {
    const plan = PLANS.find(p => p.id === planId);
    if (!plan || plan.id === 'free') {
      throw new Error('Invalid plan');
    }

    const session = await stripe.checkout.sessions.create({
      customer_email: undefined, // Will be filled by Stripe
      line_items: [
        {
          price: plan.stripePriceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXT_PUBLIC_BASE_URL}/dashboard?success=true`,
      cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL}/pricing?canceled=true`,
      metadata: {
        userId,
        planId
      }
    });

    return session;
  }

  async handleWebhook(event: Stripe.Event) {
    switch (event.type) {
      case 'checkout.session.completed':
        await this.handleSubscriptionCreated(event.data.object as Stripe.Checkout.Session);
        break;
      case 'invoice.payment_succeeded':
        await this.handlePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;
      case 'customer.subscription.deleted':
        await this.handleSubscriptionCanceled(event.data.object as Stripe.Subscription);
        break;
    }
  }

  private async handleSubscriptionCreated(session: Stripe.Checkout.Session) {
    const { userId, planId } = session.metadata!;

    // Update user subscription in database
    await supabase
      .from('subscriptions')
      .update({
        plan: planId,
        stripe_customer_id: session.customer as string,
        renders_used: 0,
        renders_limit: PLANS.find(p => p.id === planId)?.renders || -1
      })
      .eq('user_id', userId);
  }
}
```

**Pricing Page** (`pages/pricing.tsx`):
```typescript
import { useState } from 'react';
import { Check, Zap, Crown, Building } from 'lucide-react';
import { PLANS, PaymentService } from '../lib/stripe';

export default function Pricing() {
  const [loading, setLoading] = useState<string | null>(null);
  const paymentService = new PaymentService();

  const handleUpgrade = async (planId: string) => {
    setLoading(planId);
    try {
      const session = await paymentService.createCheckoutSession(planId, 'user-id'); // Get from auth
      window.location.href = session.url!;
    } catch (error) {
      alert('Something went wrong. Please try again.');
    } finally {
      setLoading(null);
    }
  };

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'personal': return <Zap className="h-6 w-6" />;
      case 'pro': return <Crown className="h-6 w-6" />;
      case 'business': return <Building className="h-6 w-6" />;
      default: return null;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-16">
      <div className="text-center mb-16">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Choose Your Sustainable Design Plan
        </h1>
        <p className="text-xl text-gray-600">
          Transform unlimited rooms with eco-friendly AI design
        </p>
      </div>

      <div className="grid md:grid-cols-4 gap-8">
        {PLANS.map((plan) => (
          <div
            key={plan.id}
            className={`relative rounded-2xl border-2 p-8 ${
              plan.id === 'personal'
                ? 'border-green-500 bg-green-50'
                : 'border-gray-200 bg-white'
            }`}
          >
            {plan.id === 'personal' && (
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-green-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                  Most Popular
                </span>
              </div>
            )}

            <div className="text-center">
              <div className="flex justify-center mb-4">
                {getPlanIcon(plan.id)}
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
              <div className="mb-6">
                <span className="text-4xl font-bold text-gray-900">${plan.price}</span>
                {plan.price > 0 && <span className="text-gray-600">/month</span>}
              </div>
            </div>

            <ul className="space-y-3 mb-8">
              {plan.features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <Check className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">{feature}</span>
                </li>
              ))}
            </ul>

            <button
              onClick={() => plan.id !== 'free' && handleUpgrade(plan.id)}
              disabled={loading === plan.id || plan.id === 'free'}
              className={`w-full py-3 rounded-lg font-semibold transition-colors ${
                plan.id === 'free'
                  ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                  : plan.id === 'personal'
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-gray-900 text-white hover:bg-gray-800'
              }`}
            >
              {loading === plan.id ? 'Loading...' :
               plan.id === 'free' ? 'Current Plan' : 'Upgrade Now'}
            </button>
          </div>
        ))}
      </div>

      {/* FAQ Section */}
      <div className="mt-20">
        <h2 className="text-3xl font-bold text-center mb-12">Frequently Asked Questions</h2>
        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <h3 className="font-semibold mb-2">Can I cancel anytime?</h3>
            <p className="text-gray-600">Yes! Cancel your subscription anytime with no penalties.</p>
          </div>
          <div>
            <h3 className="font-semibold mb-2">What's included in unlimited designs?</h3>
            <p className="text-gray-600">Generate as many room designs as you want with no monthly limits.</p>
          </div>
          <div>
            <h3 className="font-semibold mb-2">How eco-friendly is the furniture?</h3>
            <p className="text-gray-600">All furniture partners meet our 80%+ recyclability and low carbon footprint standards.</p>
          </div>
          <div>
            <h3 className="font-semibold mb-2">Do you offer refunds?</h3>
            <p className="text-gray-600">We offer a 30-day money-back guarantee if you're not satisfied.</p>
          </div>
        </div>
      </div>
    </div>
  );
}
```

**Deliverable**: Complete payment system with 4 subscription tiers

### **Sub-Phase 5B: Subscription Management** (Days 32-35)
**What we're building**: Dashboard where users can see and manage their subscription

**Like explaining to a 5-year-old**:
*"This is like a report card that shows how much you've used our app and lets you change your plan if you want more or less features!"*

**Subscription Dashboard** (`components/SubscriptionDashboard.tsx`):
```typescript
import { useState, useEffect } from 'react';
import { CreditCard, Calendar, TrendingUp, Settings } from 'lucide-react';
import { PLANS } from '../lib/stripe';

interface Subscription {
  plan: string;
  renders_used: number;
  renders_limit: number;
  stripe_customer_id: string;
  created_at: string;
}

export default function SubscriptionDashboard({ userId }: { userId: string }) {
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [billingHistory, setBillingHistory] = useState([]);

  useEffect(() => {
    loadSubscription();
  }, [userId]);

  const loadSubscription = async () => {
    const { data } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    setSubscription(data);
  };

  const currentPlan = PLANS.find(p => p.id === subscription?.plan);
  const usagePercentage = subscription?.renders_limit === -1
    ? 0
    : (subscription?.renders_used || 0) / (subscription?.renders_limit || 1) * 100;

  return (
    <div className="space-y-6">
      {/* Current Plan */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <CreditCard className="h-5 w-5 text-green-600 mr-2" />
          Current Plan
        </h2>

        {currentPlan && (
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-2xl font-bold text-gray-900">{currentPlan.name}</h3>
              <p className="text-gray-600">
                ${currentPlan.price}/month
                {currentPlan.price === 0 && ' (Free)'}
              </p>
              <div className="mt-4 space-y-2">
                {currentPlan.features.slice(0, 3).map((feature, index) => (
                  <div key={index} className="flex items-center text-sm text-gray-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    {feature}
                  </div>
                ))}
              </div>
            </div>

            <div className="text-right">
              <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                {currentPlan.id === 'free' ? 'Upgrade Plan' : 'Manage Billing'}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Usage Stats */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <TrendingUp className="h-5 w-5 text-blue-600 mr-2" />
          Usage This Month
        </h2>

        <div className="grid md:grid-cols-3 gap-6">
          <div>
            <div className="text-3xl font-bold text-blue-600">
              {subscription?.renders_used || 0}
            </div>
            <div className="text-gray-600">Designs Created</div>
          </div>

          <div>
            <div className="text-3xl font-bold text-green-600">
              {subscription?.renders_limit === -1 ? '∞' : subscription?.renders_limit || 3}
            </div>
            <div className="text-gray-600">Monthly Limit</div>
          </div>

          <div>
            <div className="text-3xl font-bold text-purple-600">
              {subscription?.renders_limit === -1 ? '0' : Math.round(usagePercentage)}%
            </div>
            <div className="text-gray-600">Used</div>
          </div>
        </div>

        {subscription?.renders_limit !== -1 && (
          <div className="mt-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Usage Progress</span>
              <span>{subscription?.renders_used || 0} / {subscription?.renders_limit || 3}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(usagePercentage, 100)}%` }}
              ></div>
            </div>

            {usagePercentage > 80 && (
              <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-yellow-800 text-sm">
                  ⚠️ You're running low on designs! Consider upgrading to unlimited.
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="grid md:grid-cols-2 gap-4">
        <button className="bg-white border border-gray-200 rounded-lg p-4 text-left hover:bg-gray-50 transition-colors">
          <Settings className="h-6 w-6 text-gray-600 mb-2" />
          <h3 className="font-semibold">Account Settings</h3>
          <p className="text-sm text-gray-600">Update your profile and preferences</p>
        </button>

        <button className="bg-white border border-gray-200 rounded-lg p-4 text-left hover:bg-gray-50 transition-colors">
          <Calendar className="h-6 w-6 text-gray-600 mb-2" />
          <h3 className="font-semibold">Billing History</h3>
          <p className="text-sm text-gray-600">View past invoices and payments</p>
        </button>
      </div>
    </div>
  );
}
```

**Deliverable**: Complete subscription management system with usage tracking

## **Week 6: Affiliate System & Partner Integration**

### **Sub-Phase 6A: Affiliate Tracking System** (Days 36-38)
**What we're building**: System to track when people buy furniture and give us money

**Like explaining to a 5-year-old**:
*"When someone clicks on a chair in our app and buys it from the store, the store gives us a small thank-you gift (money) for sending them a customer!"*

**Affiliate Tracking** (`lib/affiliate-service.ts`):
```typescript
export interface AffiliateClick {
  id: string;
  user_id: string;
  furniture_id: string;
  partner_name: string;
  click_timestamp: string;
  conversion_timestamp?: string;
  commission_amount?: number;
  order_value?: number;
}

export class AffiliateService {
  async trackClick(userId: string, furnitureId: string, partnerName: string) {
    // Generate unique tracking ID
    const trackingId = `click_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Store click in database
    const { data, error } = await supabase
      .from('affiliate_clicks')
      .insert({
        id: trackingId,
        user_id: userId,
        furniture_id: furnitureId,
        partner_name: partnerName,
        click_timestamp: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;

    // Track in analytics
    posthog.capture('affiliate_click', {
      tracking_id: trackingId,
      furniture_id: furnitureId,
      partner_name: partnerName,
      user_id: userId
    });

    return trackingId;
  }

  async generateAffiliateLink(furnitureId: string, userId: string): Promise<string> {
    const furniture = await this.getFurniture(furnitureId);
    const trackingId = await this.trackClick(userId, furnitureId, furniture.partner_name);

    // Add tracking parameters to partner URL
    const url = new URL(furniture.partner_url);
    url.searchParams.append('ref', 'ecodesignai');
    url.searchParams.append('tracking_id', trackingId);
    url.searchParams.append('utm_source', 'ecodesignai');
    url.searchParams.append('utm_medium', 'affiliate');
    url.searchParams.append('utm_campaign', 'furniture_click');

    return url.toString();
  }

  async recordConversion(trackingId: string, orderValue: number, commissionRate: number) {
    const commissionAmount = orderValue * (commissionRate / 100);

    const { error } = await supabase
      .from('affiliate_clicks')
      .update({
        conversion_timestamp: new Date().toISOString(),
        order_value: orderValue,
        commission_amount: commissionAmount
      })
      .eq('id', trackingId);

    if (error) throw error;

    // Track conversion in analytics
    posthog.capture('affiliate_conversion', {
      tracking_id: trackingId,
      order_value: orderValue,
      commission_amount: commissionAmount
    });

    return commissionAmount;
  }

  async getAffiliateStats(dateRange: { start: Date; end: Date }) {
    const { data, error } = await supabase
      .from('affiliate_clicks')
      .select('*')
      .gte('click_timestamp', dateRange.start.toISOString())
      .lte('click_timestamp', dateRange.end.toISOString());

    if (error) throw error;

    const totalClicks = data.length;
    const conversions = data.filter(click => click.conversion_timestamp);
    const totalCommission = conversions.reduce((sum, conv) => sum + (conv.commission_amount || 0), 0);
    const conversionRate = totalClicks > 0 ? (conversions.length / totalClicks) * 100 : 0;

    return {
      totalClicks,
      totalConversions: conversions.length,
      conversionRate,
      totalCommission,
      averageOrderValue: conversions.length > 0
        ? conversions.reduce((sum, conv) => sum + (conv.order_value || 0), 0) / conversions.length
        : 0
    };
  }
}
```

**Enhanced Furniture Component with Tracking**:
```typescript
import { AffiliateService } from '../lib/affiliate-service';

const FurnitureCard = ({ furniture, userId }) => {
  const affiliateService = new AffiliateService();

  const handleFurnitureClick = async () => {
    try {
      const affiliateLink = await affiliateService.generateAffiliateLink(furniture.id, userId);
      window.open(affiliateLink, '_blank');
    } catch (error) {
      console.error('Failed to generate affiliate link:', error);
      // Fallback to direct link
      window.open(furniture.partner_url, '_blank');
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
      <img
        src={furniture.image_url}
        alt={furniture.name}
        className="w-full h-48 object-cover"
      />
      <div className="p-4">
        <h3 className="font-semibold text-gray-900 mb-2">{furniture.name}</h3>
        <p className="text-green-600 font-medium mb-2">{furniture.price_range}</p>

        <div className="flex justify-between items-center text-sm text-gray-600 mb-3">
          <div className="flex items-center">
            <Leaf className="h-4 w-4 mr-1" />
            {furniture.carbon_footprint}kg CO₂
          </div>
          <div>
            {furniture.recyclability_percentage}% recyclable
          </div>
        </div>

        <button
          onClick={handleFurnitureClick}
          className="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition-colors"
        >
          Shop Eco-Friendly
        </button>
      </div>
    </div>
  );
};
```

**Deliverable**: Complete affiliate tracking system with commission calculation

### **Sub-Phase 6B: Partner Dashboard & Analytics** (Days 39-42)
**What we're building**: Dashboard to see how much money we're making from furniture sales

**Like explaining to a 5-year-old**:
*"This is like a scoreboard that shows us how many people clicked on furniture and bought things, and how much thank-you money we earned!"*

**Affiliate Analytics Dashboard** (`components/AffiliateDashboard.tsx`):
```typescript
import { useState, useEffect } from 'react';
import { DollarSign, MousePointer, TrendingUp, ShoppingBag } from 'lucide-react';
import { AffiliateService } from '../lib/affiliate-service';

export default function AffiliateDashboard() {
  const [stats, setStats] = useState(null);
  const [timeRange, setTimeRange] = useState('30'); // days
  const affiliateService = new AffiliateService();

  useEffect(() => {
    loadStats();
  }, [timeRange]);

  const loadStats = async () => {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(timeRange));

    const data = await affiliateService.getAffiliateStats({
      start: startDate,
      end: endDate
    });

    setStats(data);
  };

  if (!stats) return <div>Loading affiliate stats...</div>;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Affiliate Performance</h2>
        <select
          value={timeRange}
          onChange={(e) => setTimeRange(e.target.value)}
          className="border border-gray-300 rounded-lg px-3 py-2"
        >
          <option value="7">Last 7 days</option>
          <option value="30">Last 30 days</option>
          <option value="90">Last 90 days</option>
        </select>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Clicks</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalClicks}</p>
            </div>
            <MousePointer className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Conversions</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalConversions}</p>
            </div>
            <ShoppingBag className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Conversion Rate</p>
              <p className="text-2xl font-bold text-gray-900">{stats.conversionRate.toFixed(1)}%</p>
            </div>
            <TrendingUp className="h-8 w-8 text-purple-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Commission Earned</p>
              <p className="text-2xl font-bold text-gray-900">${stats.totalCommission.toFixed(2)}</p>
            </div>
            <DollarSign className="h-8 w-8 text-green-600" />
          </div>
        </div>
      </div>

      {/* Performance Insights */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4">Performance Insights</h3>
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Average Order Value</h4>
            <p className="text-3xl font-bold text-green-600">${stats.averageOrderValue.toFixed(2)}</p>
            <p className="text-sm text-gray-600 mt-1">Per successful conversion</p>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Revenue per Click</h4>
            <p className="text-3xl font-bold text-blue-600">
              ${stats.totalClicks > 0 ? (stats.totalCommission / stats.totalClicks).toFixed(2) : '0.00'}
            </p>
            <p className="text-sm text-gray-600 mt-1">Commission per click</p>
          </div>
        </div>
      </div>

      {/* Recommendations */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-green-800 mb-3">💡 Optimization Tips</h3>
        <ul className="space-y-2 text-green-700">
          {stats.conversionRate < 2 && (
            <li>• Your conversion rate is below average. Consider featuring more popular furniture items.</li>
          )}
          {stats.averageOrderValue < 200 && (
            <li>• Focus on promoting higher-value furniture to increase commission per sale.</li>
          )}
          {stats.totalClicks < 100 && (
            <li>• Increase user engagement by improving furniture placement in designs.</li>
          )}
          <li>• Partner with more eco-friendly furniture brands to expand your catalog.</li>
        </ul>
      </div>
    </div>
  );
}
```

**Deliverable**: Complete affiliate analytics dashboard with performance insights

**🎉 END OF WEEK 6**: We now have a complete monetization system!

---

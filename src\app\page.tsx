"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";

const STYLES = [
  { label: "Minimalist", image: "https://ext.same-assets.com/4123950039/1267053207.jpeg", id: 1 },
  { label: "Scandinavian", image: "https://ext.same-assets.com/4123950039/4031576135.jpeg", id: 2 },
  { label: "Industrial", image: "https://ext.same-assets.com/4123950039/2311013961.jpeg", id: 3 },
  { label: "Mid-Century Modern", image: "https://ext.same-assets.com/4123950039/4247774748.jpeg", id: 4 },
  { label: "Bohemian", image: "https://ext.same-assets.com/4123950039/2162116070.jpeg", id: 5 },
  { label: "Art Deco", image: "https://ext.same-assets.com/4123950039/3217488218.jpeg", id: 6 },
  { label: "Coastal", image: "https://ext.same-assets.com/4123950039/4253859114.jpeg", id: 7 },
  { label: "Farmhouse", image: "https://ext.same-assets.com/4123950039/2462314040.jpeg", id: 8 },
  { label: "Traditional", image: "https://ext.same-assets.com/4123950039/362001557.jpeg", id: 9 },
  { label: "French Country", image: "https://ext.same-assets.com/4123950039/690654409.jpeg", id: 16 },
  { label: "Moroccan", image: "https://ext.same-assets.com/4123950039/2521446536.jpeg", id: 26 },
  { label: "Modern", image: "https://ext.same-assets.com/4123950039/1390157197.jpeg", id: 10 },
  { label: "Transitional", image: "https://ext.same-assets.com/4123950039/891312444.jpeg", id: 11 },
  { label: "Shabby Chic", image: "https://ext.same-assets.com/4123950039/1011160405.jpeg", id: 12 },
  { label: "Japanese Zen", image: "https://ext.same-assets.com/4123950039/3933773814.jpeg", id: 13 },
  { label: "Contemporary", image: "https://ext.same-assets.com/4123950039/3246906598.jpeg", id: 14 },
  { label: "Gothic", image: "https://ext.same-assets.com/4123950039/1038660028.jpeg", id: 15 },
  { label: "Hollywood Regency", image: "https://ext.same-assets.com/4123950039/927860819.jpeg", id: 17 },
  { label: "Rustic", image: "https://ext.same-assets.com/4123950039/1650356877.jpeg", id: 18 },
  { label: "Southwestern", image: "https://ext.same-assets.com/4123950039/2422832776.jpeg", id: 19 },
  { label: "Victorian", image: "https://ext.same-assets.com/4123950039/180188305.jpeg", id: 20 },
  { label: "Steampunk", image: "https://ext.same-assets.com/4123950039/650580268.jpeg", id: 21 },
  { label: "Biophilic", image: "https://ext.same-assets.com/4123950039/3597336419.jpeg", id: 22 },
  { label: "Futuristic", image: "https://ext.same-assets.com/4123950039/1432023951.jpeg", id: 23 },
  { label: "Maximalist", image: "https://ext.same-assets.com/4123950039/1389072092.jpeg", id: 24 },
  { label: "Pop Art", image: "https://ext.same-assets.com/4123950039/3540851178.jpeg", id: 25 },
  { label: "Cosmic Chic", image: "https://ext.same-assets.com/4123950039/3420677188.jpeg", id: 27 },
  { label: "Wabi-Sabi", image: "https://ext.same-assets.com/4123950039/744976908.jpeg", id: 28 },
  { label: "Tropical", image: "https://ext.same-assets.com/4123950039/2401809942.jpeg", id: 29 },
  { label: "Vintage Glam", image: "https://ext.same-assets.com/4123950039/1246017528.jpeg", id: 30 },
  { label: "Cyberpunk", image: "https://ext.same-assets.com/4123950039/2253538115.jpeg", id: 31 },
  { label: "Psychedelic", image: "https://ext.same-assets.com/4123950039/3683077406.jpeg", id: 32 },
  { label: "Surrealist", image: "https://ext.same-assets.com/4123950039/1098851357.jpeg", id: 33 },
  { label: "Post-Apocalyptic", image: "https://ext.same-assets.com/4123950039/576171152.jpeg", id: 34 },
  { label: "Candy Land", image: "https://ext.same-assets.com/4123950039/3193769849.jpeg", id: 35 }
];

const DEMO_RESULTS = [
  "https://ext.same-assets.com/4123950039/2719286588.jpeg",
  "https://ext.same-assets.com/4123950039/3970137075.jpeg"
];

export default function HomePage() {
  const [selected, setSelected] = useState<string[]>([]);
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [step, setStep] = useState<'initial'|'loading'|'done'|'error'>('initial');
  const [showIndex, setShowIndex] = useState(0);
  const router = useRouter();

  function toggle(style: string) {
    setSelected((list) =>
      list.includes(style)
        ? list.filter((s) => s !== style)
        : list.length < 5
        ? [...list, style]
        : list
    );
  }

  function handleStyleClick(style: typeof STYLES[number]) {
    if (typeof window !== "undefined") {
      localStorage.setItem("preselected_style_label", style.label);
    }
    router.push(`/try?room_style_ids[]=${style.id}`);
  }

  function onFileChange(e: React.ChangeEvent<HTMLInputElement>) {
    const f = e.target.files?.[0] || null;
    setFile(f);
    if (f) setPreview(URL.createObjectURL(f));
    else setPreview(null);
  }

  function handleRedesign() {
    setStep('loading');
    setTimeout(() => {
      if (Math.random() < 0.2) setStep('error');
      else setStep('done');
      setShowIndex(0);
    }, 2200);
  }
  function handleRetry() { setStep('initial'); }
  function handleBack() { setStep('initial'); setFile(null); setPreview(null); }

  return (
    <section className="w-full bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 pb-14">
      <style>{`
        .card-fadein {
          opacity: 0;
          animation: card-fadein 0.5s forwards;
        }
        @keyframes card-fadein {
          to { opacity: 1; }
        }
        .card-slideup {
          opacity: 0;
          transform: translateY(32px);
          animation: card-slideup 0.7s cubic-bezier(.4,1.2,.4,1) forwards;
        }
        @keyframes card-slideup {
          to {
            opacity: 1;
            transform: none;
          }
        }
        @media (max-width: 640px) {
          .card-fadein { animation-duration: 0.35s !important; }
          .card-slideup { animation-duration: 0.45s !important; }
        }
      `}</style>
      <div className="max-w-6xl mx-auto px-2 sm:px-4 pt-16 flex flex-col gap-8">
        <div className="flex flex-col md:flex-row gap-12 md:gap-20 items-center md:items-start justify-between card-slideup">
          <div className="flex-1 min-w-[260px] md:mt-20">
            <h1 className="text-3xl sm:text-4xl md:text-6xl font-bold mb-5 text-slate-50 tracking-tight leading-tight">
              Design your dream home
            </h1>
            <p className="text-lg sm:text-xl mb-7 text-gray-300 max-w-xl">
              Turn your ideas into professional interior designs, instantly. Upload your own room or generate a new look with AI — with 40+ unique styles to try.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 mb-4">
              <a
                href="/start"
                className="rounded-lg bg-teal-600 px-6 py-3 text-base sm:text-lg font-semibold text-white shadow transition-all hover:bg-teal-500 hover:shadow-lg border border-teal-400 hover:border-teal-300 active:scale-95 duration-150"
              >
                Design your interior
              </a>
              <a href="/pricing" className="rounded-lg border border-gray-700 px-6 py-3 text-base sm:text-lg font-semibold text-gray-50 hover:bg-gray-800 hover:border-gray-500 transition-all active:scale-95 duration-150">See pricing</a>
            </div>
            <div className="text-gray-400 text-xs mt-1">321 other people already tried it today!</div>
          </div>
          <div className="flex-1 max-w-lg w-full">
            <div className="h-64 sm:h-80 w-full rounded-2xl bg-gray-800/70 flex items-center justify-center shadow-xl border border-gray-900 card-fadein" style={{ animationDelay: "0.15s" }}>
              <img className="object-contain w-full h-4/5 rounded-xl" src="https://ext.same-assets.com/4123950039/3970137075.jpeg" alt="RoomAI example" />
            </div>
          </div>
        </div>
        <section className="mt-8">
          <h2 className="text-xl sm:text-2xl text-white font-semibold mb-3">
            What's your design style? <span className="font-normal text-sm sm:text-base text-gray-400">You can choose up to 5 styles</span>
          </h2>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-3 sm:gap-4">
            {STYLES.map((style, idx) => (
              <button
                key={style.label}
                onClick={() => handleStyleClick(style)}
                className={`group flex flex-col items-stretch justify-end rounded-xl border-2 overflow-hidden bg-gray-900 transition-all h-36 sm:h-40 relative shadow-md card-fadein ${
                  selected.includes(style.label)
                    ? "border-teal-500 ring-4 ring-teal-300/40"
                    : "border-gray-700 hover:border-teal-500 hover:z-10 scale-100 hover:scale-105 active:scale-95 duration-150"
                }`}
                style={{ animationDelay: `${0.03 * idx}s` }}
                aria-pressed={selected.includes(style.label)}
                type="button"
              >
                <img
                  src={style.image}
                  alt={style.label}
                  className="object-cover w-full h-24 sm:h-28 group-hover:scale-105 duration-300 rounded-t-xl"
                  draggable={false}
                />
                <span
                  className={`px-3 py-1 text-sm sm:text-base font-semibold text-left truncate ${
                    selected.includes(style.label) ? "text-teal-400" : "text-white"
                  }`}
                >
                  {style.label}
                </span>
              </button>
            ))}
          </div>
          <div className="mt-4 text-sm text-teal-400 font-medium h-5">
            {selected.length > 0
              ? `${selected.length} style${selected.length > 1 ? "s" : ""} selected`
              : null}
          </div>
        </section>
        <div className="mt-12 flex flex-col items-center md:items-stretch md:flex-row gap-12">
          <div className="bg-gray-900 border-2 border-gray-800 rounded-2xl shadow-lg max-w-lg mx-auto w-full p-4 sm:p-8 relative flex flex-col items-center card-slideup card-fadein" style={{ animationDelay: "0.18s" }}>
            {step === 'initial' && (
              <div className="w-full flex flex-col items-center justify-center gap-4">
                <label htmlFor="room-upload" className="w-full cursor-pointer flex flex-col items-center justify-center border-2 border-dashed border-teal-400/40 rounded-xl h-44 sm:h-52 bg-gray-950/80 hover:border-teal-500 transition-all relative group card-fadein" style={{ animationDelay: "0.22s" }}>
                  {preview ? (
                    <img src={preview} alt="Preview" className="object-cover w-full h-full rounded-xl" />
                  ) : (
                    <>
                      <svg className="w-10 h-10 sm:w-12 sm:h-12 text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16V4m0 0A4 4 0 0115 4M7 4a4 4 0 018 0m0 0v12m6 4H5a2 2 0 01-2-2V8a2 2 0 012-2h4l2-2 2 2h4a2 2 0 012 2v10a2 2 0 01-2 2z" />
                      </svg>
                      <span className="text-gray-300 text-base font-medium">Upload a photo of your room</span>
                      <span className="text-gray-500 text-xs mt-2 px-5 text-center">We'll redesign it with AI.<br />Take a picture in daylight. Hide nonessential items. Use the regular 1x lens.</span>
                    </>
                  )}
                  <input id="room-upload" type="file" accept="image/*" className="hidden" onChange={onFileChange} />
                </label>
                <button className="mt-5 mb-2 w-full px-5 py-3 rounded-lg bg-teal-600 font-semibold text-white text-base sm:text-lg border border-teal-400 hover:bg-teal-500 hover:border-teal-300 transition active:scale-95 duration-150 disabled:opacity-60" disabled={!file || selected.length === 0} onClick={handleRedesign}>Redesign with AI</button>
              </div>
            )}
            {step === 'loading' && (
              <div className="flex flex-col gap-6 items-center w-full py-16">
                <svg className="w-10 h-10 sm:w-12 sm:h-12 animate-spin text-teal-400 mb-3" viewBox="0 0 40 40" fill="none"><circle cx="20" cy="20" r="18" stroke="#2DD4BF" strokeWidth="4" strokeDasharray="36 100" /><circle cx="20" cy="20" r="18" stroke="#134E4A" strokeWidth="4" opacity="0.1" /></svg>
                <div className="text-base sm:text-lg text-gray-200 font-medium text-center">Your AI redesign is generating… <br /><span className="text-teal-400">This usually takes 10–30 seconds.</span></div>
              </div>
            )}
            {step === 'done' && (
              <div className="flex flex-col items-center w-full gap-4">
                <div className="mb-4 w-full relative rounded-xl border-2 border-gray-700 bg-gray-950 p-2 flex items-center justify-center min-h-56 sm:min-h-72 card-fadein" style={{ animationDelay: "0.25s" }}>
                  <img src={DEMO_RESULTS[showIndex]} className="object-cover max-h-56 sm:max-h-80 rounded-lg transition-all duration-400 shadow-lg" alt="AI Result" />
                  {DEMO_RESULTS.length > 1 && (
                    <div className="absolute z-10 bottom-2 sm:bottom-3 left-0 right-0 flex justify-center gap-3 sm:gap-2">
                      {DEMO_RESULTS.map((_, idx) => (
                        <button
                          key={idx}
                          onClick={() => setShowIndex(idx)}
                          className={`inline-block w-4 h-4 sm:w-3 sm:h-3 rounded-full border-2 transition-all duration-150 ${showIndex === idx ? "bg-teal-400 border-teal-400 scale-110" : "bg-gray-900 border-gray-500"}`}
                          aria-label={`Show result ${idx + 1}`}
                        />
                      ))}
                    </div>
                  )}
                </div>
                <div className="flex gap-3 w-full">
                  <button className="flex-1 rounded-lg bg-gray-800 text-white text-base font-medium py-3 px-5 border border-gray-700 hover:bg-gray-700 transition active:scale-95 duration-150" onClick={handleBack}>Back</button>
                  <button className="flex-1 rounded-lg bg-teal-600 text-white text-base font-medium py-3 px-5 border border-teal-400 hover:bg-teal-500 transition active:scale-95 duration-150" onClick={handleRetry}>Try another</button>
                </div>
              </div>
            )}
            {step === 'error' && (
              <div className="flex flex-col gap-4 items-center w-full py-10">
                <svg className="w-8 h-8 sm:w-10 sm:h-10 text-red-500" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" strokeOpacity="0.4"/><path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4m0 4h.01" /></svg>
                <div className="text-base text-red-300 pb-2 font-medium">Something went wrong. Please try again!</div>
                <button className="px-6 py-2 rounded-lg bg-teal-600 text-white font-semibold border border-teal-400 hover:bg-teal-500 transition-all active:scale-95 duration-150" onClick={handleRetry}>Try Again</button>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}

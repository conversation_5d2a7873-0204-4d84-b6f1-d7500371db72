import React from 'react';

interface IconProps {
  size?: number;
  color?: string;
  className?: string;
}

// Wood Materials Icon
export const WoodIcon: React.FC<IconProps> = ({ size = 24, color = "#8B4513", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <pattern id="woodGrain" patternUnits="userSpaceOnUse" width="4" height="24">
        <path d="M0,0 Q2,6 0,12 Q2,18 0,24" stroke={color} strokeWidth="0.5" fill="none" opacity="0.3"/>
        <path d="M2,0 Q4,6 2,12 Q4,18 2,24" stroke={color} strokeWidth="0.3" fill="none" opacity="0.2"/>
      </pattern>
    </defs>
    <rect width="24" height="24" fill={color} rx="2"/>
    <rect width="24" height="24" fill="url(#woodGrain)" rx="2"/>
    <path d="M2,4 Q12,8 22,4 M2,8 Q12,12 22,8 M2,12 Q12,16 22,12 M2,16 Q12,20 22,16 M2,20 Q12,24 22,20" 
          stroke={color} strokeWidth="0.5" fill="none" opacity="0.4"/>
  </svg>
);

// Metal Materials Icon
export const MetalIcon: React.FC<IconProps> = ({ size = 24, color = "#C0C0C0", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="metalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor={color} stopOpacity="1"/>
        <stop offset="30%" stopColor="#FFFFFF" stopOpacity="0.8"/>
        <stop offset="70%" stopColor={color} stopOpacity="0.6"/>
        <stop offset="100%" stopColor="#000000" stopOpacity="0.3"/>
      </linearGradient>
      <linearGradient id="metalShine" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="transparent"/>
        <stop offset="45%" stopColor="#FFFFFF" stopOpacity="0.8"/>
        <stop offset="55%" stopColor="#FFFFFF" stopOpacity="0.8"/>
        <stop offset="100%" stopColor="transparent"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#metalGradient)" rx="2"/>
    <rect x="8" y="0" width="8" height="24" fill="url(#metalShine)" rx="1"/>
    <circle cx="6" cy="6" r="1" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="18" cy="18" r="1" fill="#FFFFFF" opacity="0.6"/>
  </svg>
);

// Stone Materials Icon
export const StoneIcon: React.FC<IconProps> = ({ size = 24, color = "#808080", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <filter id="stoneTexture">
        <feTurbulence baseFrequency="0.9" numOctaves="4" result="noise"/>
        <feDisplacementMap in="SourceGraphic" in2="noise" scale="1"/>
      </filter>
    </defs>
    <rect width="24" height="24" fill={color} rx="3"/>
    <path d="M0,8 L8,0 L16,0 L24,8 L24,16 L16,24 L8,24 L0,16 Z" fill={color} opacity="0.8"/>
    <circle cx="7" cy="7" r="2" fill={color} opacity="0.6" filter="url(#stoneTexture)"/>
    <circle cx="17" cy="12" r="1.5" fill={color} opacity="0.4"/>
    <circle cx="12" cy="18" r="1" fill={color} opacity="0.5"/>
    <path d="M3,3 L6,6 M18,3 L21,6 M3,21 L6,18 M18,21 L21,18" stroke={color} strokeWidth="1" opacity="0.3"/>
  </svg>
);

// Fabric Materials Icon
export const FabricIcon: React.FC<IconProps> = ({ size = 24, color = "#DEB887", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <pattern id="fabricWeave" patternUnits="userSpaceOnUse" width="4" height="4">
        <rect width="2" height="2" fill={color}/>
        <rect x="2" y="2" width="2" height="2" fill={color}/>
        <rect x="2" y="0" width="2" height="2" fill={color} opacity="0.7"/>
        <rect x="0" y="2" width="2" height="2" fill={color} opacity="0.7"/>
      </pattern>
    </defs>
    <rect width="24" height="24" fill="url(#fabricWeave)" rx="2"/>
    <path d="M0,0 L24,0 M0,6 L24,6 M0,12 L24,12 M0,18 L24,18 M0,24 L24,24" 
          stroke={color} strokeWidth="0.5" opacity="0.3"/>
    <path d="M0,0 L0,24 M6,0 L6,24 M12,0 L12,24 M18,0 L18,24 M24,0 L24,24" 
          stroke={color} strokeWidth="0.5" opacity="0.3"/>
  </svg>
);

// Glass Materials Icon
export const GlassIcon: React.FC<IconProps> = ({ size = 24, color = "#E6F3FF", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="glassGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor={color} stopOpacity="0.8"/>
        <stop offset="50%" stopColor="#FFFFFF" stopOpacity="0.4"/>
        <stop offset="100%" stopColor={color} stopOpacity="0.6"/>
      </linearGradient>
      <linearGradient id="glassReflection" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="transparent"/>
        <stop offset="30%" stopColor="#FFFFFF" stopOpacity="0.9"/>
        <stop offset="70%" stopColor="#FFFFFF" stopOpacity="0.9"/>
        <stop offset="100%" stopColor="transparent"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#glassGradient)" rx="2" stroke="#B3D9FF" strokeWidth="1"/>
    <rect x="4" y="2" width="16" height="4" fill="url(#glassReflection)" rx="1"/>
    <circle cx="18" cy="18" r="2" fill="#FFFFFF" opacity="0.7"/>
    <path d="M2,2 L6,6 M18,2 L22,6" stroke="#FFFFFF" strokeWidth="1" opacity="0.5"/>
  </svg>
);

// Ceramic Materials Icon
export const CeramicIcon: React.FC<IconProps> = ({ size = 24, color = "#F5DEB3", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="ceramicGradient" cx="50%" cy="30%" r="70%">
        <stop offset="0%" stopColor="#FFFFFF" stopOpacity="0.8"/>
        <stop offset="50%" stopColor={color} stopOpacity="0.9"/>
        <stop offset="100%" stopColor={color} stopOpacity="1"/>
      </radialGradient>
    </defs>
    <rect width="24" height="24" fill="url(#ceramicGradient)" rx="2"/>
    <ellipse cx="12" cy="8" rx="8" ry="2" fill="#FFFFFF" opacity="0.4"/>
    <path d="M4,4 Q12,2 20,4 Q20,8 12,10 Q4,8 4,4" fill="#FFFFFF" opacity="0.3"/>
    <circle cx="8" cy="16" r="1" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="16" cy="20" r="0.5" fill="#FFFFFF" opacity="0.5"/>
  </svg>
);

// Synthetic Materials Icon
export const SyntheticIcon: React.FC<IconProps> = ({ size = 24, color = "#FF6B6B", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="syntheticGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor={color}/>
        <stop offset="50%" stopColor="#FFFFFF" stopOpacity="0.3"/>
        <stop offset="100%" stopColor={color} stopOpacity="0.8"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#syntheticGradient)" rx="2"/>
    <polygon points="12,2 22,8 22,16 12,22 2,16 2,8" fill={color} opacity="0.7"/>
    <polygon points="12,6 18,9 18,15 12,18 6,15 6,9" fill="#FFFFFF" opacity="0.4"/>
    <circle cx="12" cy="12" r="3" fill={color} opacity="0.8"/>
    <circle cx="12" cy="12" r="1" fill="#FFFFFF"/>
  </svg>
);

// Natural Materials Icon
export const NaturalIcon: React.FC<IconProps> = ({ size = 24, color = "#8FBC8F", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <pattern id="naturalWeave" patternUnits="userSpaceOnUse" width="6" height="6">
        <path d="M0,3 Q3,0 6,3 Q3,6 0,3" fill={color} opacity="0.6"/>
        <path d="M3,0 Q6,3 3,6 Q0,3 3,0" fill={color} opacity="0.4"/>
      </pattern>
    </defs>
    <rect width="24" height="24" fill="url(#naturalWeave)" rx="2"/>
    <path d="M2,2 Q6,6 10,2 Q14,6 18,2 Q22,6 22,10 Q18,14 22,18 Q18,22 14,18 Q10,22 6,18 Q2,22 2,18 Q6,14 2,10 Q6,6 2,2" 
          stroke={color} strokeWidth="1" fill="none" opacity="0.5"/>
    <circle cx="6" cy="6" r="1" fill={color} opacity="0.8"/>
    <circle cx="18" cy="18" r="1" fill={color} opacity="0.8"/>
    <circle cx="18" cy="6" r="0.5" fill={color} opacity="0.6"/>
    <circle cx="6" cy="18" r="0.5" fill={color} opacity="0.6"/>
  </svg>
);

// Composite Materials Icon
export const CompositeIcon: React.FC<IconProps> = ({ size = 24, color = "#9370DB", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <pattern id="compositePattern" patternUnits="userSpaceOnUse" width="8" height="8">
        <rect width="4" height="4" fill={color}/>
        <rect x="4" y="4" width="4" height="4" fill={color}/>
        <rect x="4" y="0" width="4" height="4" fill={color} opacity="0.7"/>
        <rect x="0" y="4" width="4" height="4" fill={color} opacity="0.7"/>
      </pattern>
      <linearGradient id="compositeShine" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFFFFF" stopOpacity="0.3"/>
        <stop offset="100%" stopColor="transparent"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#compositePattern)" rx="2"/>
    <rect width="24" height="24" fill="url(#compositeShine)" rx="2"/>
    <path d="M0,12 L12,0 M12,24 L24,12" stroke="#FFFFFF" strokeWidth="1" opacity="0.4"/>
    <circle cx="6" cy="6" r="1.5" fill="#FFFFFF" opacity="0.5"/>
    <circle cx="18" cy="18" r="1.5" fill="#FFFFFF" opacity="0.5"/>
  </svg>
);

// Material Icon Mapper
export const getMaterialIcon = (category: string, materialName: string) => {
  const iconProps = { size: 48, className: "rounded-lg" };
  
  // Define colors for specific materials
  const materialColors: Record<string, string> = {
    // Woods
    'Oak Wood': '#DEB887',
    'Walnut Wood': '#8B4513',
    'Cherry Wood': '#D2691E',
    'Maple Wood': '#F4A460',
    'Birch Wood': '#F5DEB3',
    'Pine Wood': '#DDD26A',
    'Mahogany Wood': '#C04000',
    'Teak Wood': '#B8860B',
    'Bamboo': '#DAA520',
    'Reclaimed Wood': '#8B7355',
    'Driftwood': '#A0522D',
    'Ebony Wood': '#2F2F2F',
    
    // Metals
    'Stainless Steel': '#C0C0C0',
    'Chrome': '#E5E5E5',
    'Brass': '#B5A642',
    'Copper': '#B87333',
    'Bronze': '#CD7F32',
    'Iron': '#464451',
    'Aluminum': '#A8A8A8',
    'Gold Finish': '#FFD700',
    'Silver Finish': '#C0C0C0',
    'Pewter': '#96A8A1',
    'Titanium': '#878681',
    'Wrought Iron': '#2F2F2F',
    
    // Stones
    'Marble': '#F8F8FF',
    'Granite': '#696969',
    'Limestone': '#F5F5DC',
    'Travertine': '#FAEBD7',
    'Slate': '#2F4F4F',
    'Quartzite': '#E6E6FA',
    'Sandstone': '#F4A460',
    'Onyx': '#0F0F0F',
    'Basalt': '#36454F',
    'River Rock': '#708090',
    'Fieldstone': '#8B7D6B',
    'Cobblestone': '#696969'
  };
  
  const color = materialColors[materialName] || getDefaultColorForCategory(category);
  
  switch (category.toLowerCase()) {
    case 'wood':
      return <WoodIcon {...iconProps} color={color} />;
    case 'metal':
      return <MetalIcon {...iconProps} color={color} />;
    case 'stone':
      return <StoneIcon {...iconProps} color={color} />;
    case 'fabric':
      return <FabricIcon {...iconProps} color={color} />;
    case 'glass':
      return <GlassIcon {...iconProps} color={color} />;
    case 'ceramic':
      return <CeramicIcon {...iconProps} color={color} />;
    case 'synthetic':
      return <SyntheticIcon {...iconProps} color={color} />;
    case 'natural':
      return <NaturalIcon {...iconProps} color={color} />;
    case 'composite':
      return <CompositeIcon {...iconProps} color={color} />;
    default:
      return <WoodIcon {...iconProps} color={color} />;
  }
};

const getDefaultColorForCategory = (category: string): string => {
  const defaults: Record<string, string> = {
    wood: '#8B4513',
    metal: '#C0C0C0',
    stone: '#808080',
    fabric: '#DEB887',
    glass: '#E6F3FF',
    ceramic: '#F5DEB3',
    synthetic: '#FF6B6B',
    natural: '#8FBC8F',
    composite: '#9370DB'
  };
  return defaults[category.toLowerCase()] || '#808080';
};

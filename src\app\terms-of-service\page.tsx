"use client";
export default function TermsPage() {
  return (
    <main className="max-w-3xl mx-auto px-4 py-16">
      <h1 className="text-3xl md:text-5xl font-bold text-white mb-6">Terms of Service for Room AI</h1>
      <section className="space-y-8 text-gray-300 bg-gray-950 rounded-xl border-2 border-gray-800 px-7 py-7">
        <div>
          <h2 className="font-bold text-xl text-teal-400 mb-1">Scope of Services</h2>
          <span>Room AI is a SaaS tool for AI-generated interior design, for personal or commercial use depending on plan.</span>
        </div>
        <div>
          <h2 className="font-bold text-xl text-teal-400 mb-1">Subscription Plans and Usage Rights</h2>
          <span>Usage rights (e.g., image ownership, commercial use) depend on your subscription tier. Check the Pricing page for specifics.</span>
        </div>
        <div>
          <h2 className="font-bold text-xl text-teal-400 mb-1">Authorized Access and Use</h2>
          <span>Do not automate, scrape, or abuse the system; generated images cannot be used for model training or building competitors.</span>
        </div>
        <div>
          <h2 className="font-bold text-xl text-teal-400 mb-1">User-Provided Content</h2>
          <span>You retain rights to images you upload; see policy for aggregation and improvement clauses.</span>
        </div>
        <div>
          <h2 className="font-bold text-xl text-teal-400 mb-1">Indemnification</h2>
          <span>You indemnify Room AI against copyright/IP claims, etc. See full document for legal specifics.</span>
        </div>
        <div>
          <h2 className="font-bold text-xl text-teal-400 mb-1">Governing Law</h2>
          <span>Singapore jurisdiction applies.</span>
        </div>
      </section>
    </main>
  );
}

import React from 'react';

interface IconProps {
  size?: number;
  className?: string;
}

// WOOD MATERIALS - Each with unique characteristics

// Oak Wood - Strong vertical grain with distinctive pattern
export const OakWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="oakGrad" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="#DEB887"/>
        <stop offset="50%" stopColor="#F4A460"/>
        <stop offset="100%" stopColor="#DEB887"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#oakGrad)" rx="2"/>
    <path d="M4,0 L4,24 M8,0 L8,24 M12,0 L12,24 M16,0 L16,24 M20,0 L20,24" stroke="#CD853F" strokeWidth="0.5" opacity="0.6"/>
    <path d="M6,2 Q8,6 6,10 Q8,14 6,18 Q8,22 6,24" stroke="#8B7355" strokeWidth="0.3" fill="none"/>
    <path d="M14,0 Q16,4 14,8 Q16,12 14,16 Q16,20 14,24" stroke="#8B7355" strokeWidth="0.3" fill="none"/>
  </svg>
);

// Walnut Wood - Rich dark swirls and burls
export const WalnutWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="walnutGrad" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#A0522D"/>
        <stop offset="70%" stopColor="#8B4513"/>
        <stop offset="100%" stopColor="#654321"/>
      </radialGradient>
    </defs>
    <rect width="24" height="24" fill="url(#walnutGrad)" rx="2"/>
    <path d="M2,12 Q6,8 12,12 Q18,16 22,12" stroke="#654321" strokeWidth="1" fill="none"/>
    <path d="M0,6 Q8,10 16,6 Q24,2 24,8" stroke="#3C1810" strokeWidth="0.8" fill="none"/>
    <ellipse cx="8" cy="8" rx="3" ry="1.5" fill="#3C1810" opacity="0.4"/>
    <ellipse cx="16" cy="16" rx="2" ry="1" fill="#3C1810" opacity="0.3"/>
  </svg>
);

// Cherry Wood - Reddish with fine straight grain
export const CherryWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="cherryGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#D2691E"/>
        <stop offset="50%" stopColor="#CD853F"/>
        <stop offset="100%" stopColor="#A0522D"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#cherryGrad)" rx="2"/>
    <path d="M0,4 Q12,2 24,4 M0,8 Q12,6 24,8 M0,12 Q12,10 24,12 M0,16 Q12,14 24,16 M0,20 Q12,18 24,20" 
          stroke="#8B4513" strokeWidth="0.4" fill="none" opacity="0.7"/>
    <circle cx="6" cy="6" r="1" fill="#8B4513" opacity="0.5"/>
    <circle cx="18" cy="14" r="0.8" fill="#8B4513" opacity="0.4"/>
  </svg>
);

// Maple Wood - Light with subtle wavy grain
export const MapleWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="mapleGrad" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" stopColor="#F5DEB3"/>
        <stop offset="50%" stopColor="#F4A460"/>
        <stop offset="100%" stopColor="#DEB887"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#mapleGrad)" rx="2"/>
    <path d="M2,0 L2,24 M6,0 L6,24 M10,0 L10,24 M14,0 L14,24 M18,0 L18,24 M22,0 L22,24" 
          stroke="#DDD26A" strokeWidth="0.3" opacity="0.4"/>
    <path d="M0,8 Q4,6 8,8 Q12,10 16,8 Q20,6 24,8" stroke="#D2B48C" strokeWidth="0.5" fill="none"/>
    <path d="M0,16 Q4,14 8,16 Q12,18 16,16 Q20,14 24,16" stroke="#D2B48C" strokeWidth="0.5" fill="none"/>
  </svg>
);

// Birch Wood - White with distinctive black horizontal marks
export const BirchWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F5DEB3" rx="2"/>
    <rect x="0" y="4" width="24" height="1" fill="#2F2F2F" opacity="0.8"/>
    <rect x="0" y="8" width="16" height="0.5" fill="#2F2F2F" opacity="0.6"/>
    <rect x="0" y="12" width="20" height="1.5" fill="#2F2F2F" opacity="0.7"/>
    <rect x="0" y="16" width="12" height="0.8" fill="#2F2F2F" opacity="0.5"/>
    <rect x="0" y="20" width="24" height="1" fill="#2F2F2F" opacity="0.8"/>
    <ellipse cx="8" cy="6" rx="2" ry="0.5" fill="#2F2F2F" opacity="0.3"/>
    <ellipse cx="16" cy="14" rx="1.5" ry="0.3" fill="#2F2F2F" opacity="0.3"/>
  </svg>
);

// Pine Wood - Light with prominent knots
export const PineWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="pineKnot1" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#8B4513"/>
        <stop offset="100%" stopColor="#DDD26A"/>
      </radialGradient>
      <radialGradient id="pineKnot2" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#A0522D"/>
        <stop offset="100%" stopColor="#DDD26A"/>
      </radialGradient>
    </defs>
    <rect width="24" height="24" fill="#DDD26A" rx="2"/>
    <path d="M0,0 L24,0 M0,6 L24,6 M0,12 L24,12 M0,18 L24,18 M0,24 L24,24" 
          stroke="#F4A460" strokeWidth="0.5" opacity="0.6"/>
    <circle cx="8" cy="8" r="2.5" fill="url(#pineKnot1)"/>
    <circle cx="16" cy="16" r="1.8" fill="url(#pineKnot2)"/>
    <circle cx="8" cy="8" r="1" fill="#654321"/>
    <circle cx="16" cy="16" r="0.7" fill="#654321"/>
  </svg>
);

// Mahogany Wood - Deep reddish-brown with interlocked grain
export const MahoganyWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="mahoganyGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#C04000"/>
        <stop offset="50%" stopColor="#8B0000"/>
        <stop offset="100%" stopColor="#654321"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#mahoganyGrad)" rx="2"/>
    <path d="M0,6 Q6,4 12,6 Q18,8 24,6 M0,12 Q6,10 12,12 Q18,14 24,12 M0,18 Q6,16 12,18 Q18,20 24,18" 
          stroke="#4A0000" strokeWidth="0.8" fill="none"/>
    <path d="M4,0 Q6,8 4,16 Q6,24 8,24 M16,0 Q18,8 16,16 Q18,24 20,24" 
          stroke="#4A0000" strokeWidth="0.6" fill="none"/>
  </svg>
);

// Teak Wood - Golden brown with natural oils
export const TeakWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="teakGrad" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="#B8860B"/>
        <stop offset="30%" stopColor="#DAA520"/>
        <stop offset="70%" stopColor="#B8860B"/>
        <stop offset="100%" stopColor="#8B7355"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#teakGrad)" rx="2"/>
    <path d="M2,2 Q12,6 22,2 M2,8 Q12,12 22,8 M2,14 Q12,18 22,14 M2,20 Q12,24 22,20" 
          stroke="#8B7355" strokeWidth="0.6" fill="none" opacity="0.7"/>
    <ellipse cx="12" cy="6" rx="4" ry="1" fill="#8B7355" opacity="0.3"/>
    <ellipse cx="12" cy="18" rx="3" ry="0.8" fill="#8B7355" opacity="0.3"/>
    <circle cx="6" cy="12" r="0.8" fill="#FFD700" opacity="0.6"/>
    <circle cx="18" cy="12" r="0.6" fill="#FFD700" opacity="0.5"/>
  </svg>
);

// Bamboo - Segmented with distinctive nodes
export const BambooIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="bambooGrad" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" stopColor="#DAA520"/>
        <stop offset="50%" stopColor="#F4A460"/>
        <stop offset="100%" stopColor="#DEB887"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#bambooGrad)" rx="2"/>
    <rect x="0" y="6" width="24" height="2" fill="#8B7355"/>
    <rect x="0" y="12" width="24" height="2" fill="#8B7355"/>
    <rect x="0" y="18" width="24" height="2" fill="#8B7355"/>
    <path d="M4,0 L4,6 M8,0 L8,6 M12,0 L12,6 M16,0 L16,6 M20,0 L20,6" stroke="#B8860B" strokeWidth="0.5"/>
    <path d="M4,8 L4,12 M8,8 L8,12 M12,8 L12,12 M16,8 L16,12 M20,8 L20,12" stroke="#B8860B" strokeWidth="0.5"/>
    <path d="M4,14 L4,18 M8,14 L8,18 M12,14 L12,18 M16,14 L16,18 M20,14 L20,18" stroke="#B8860B" strokeWidth="0.5"/>
    <path d="M4,20 L4,24 M8,20 L8,24 M12,20 L12,24 M16,20 L16,24 M20,20 L20,24" stroke="#B8860B" strokeWidth="0.5"/>
  </svg>
);

// Reclaimed Wood - Weathered with nail holes and character marks
export const ReclaimedWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="reclaimedGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#8B7355"/>
        <stop offset="30%" stopColor="#A0522D"/>
        <stop offset="70%" stopColor="#696969"/>
        <stop offset="100%" stopColor="#8B7355"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#reclaimedGrad)" rx="2"/>
    <path d="M2,4 Q8,2 14,4 Q20,6 22,4 M2,12 Q8,10 14,12 Q20,14 22,12 M2,20 Q8,18 14,20 Q20,22 22,20" 
          stroke="#654321" strokeWidth="0.8" fill="none"/>
    <circle cx="6" cy="8" r="0.8" fill="#2F2F2F"/>
    <circle cx="18" cy="16" r="0.6" fill="#2F2F2F"/>
    <rect x="10" y="2" width="0.5" height="4" fill="#2F2F2F"/>
    <rect x="14" y="18" width="0.5" height="4" fill="#2F2F2F"/>
    <path d="M4,6 L8,10 M16,14 L20,18" stroke="#2F2F2F" strokeWidth="0.5"/>
  </svg>
);

// Driftwood - Smooth, weathered, ocean-worn appearance
export const DriftwoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="driftwoodGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#D3D3D3"/>
        <stop offset="30%" stopColor="#A0522D"/>
        <stop offset="70%" stopColor="#808080"/>
        <stop offset="100%" stopColor="#696969"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#driftwoodGrad)" rx="4"/>
    <path d="M0,8 Q6,6 12,8 Q18,10 24,8 M0,16 Q6,14 12,16 Q18,18 24,16" 
          stroke="#696969" strokeWidth="1" fill="none" opacity="0.6"/>
    <ellipse cx="8" cy="12" rx="3" ry="1.5" fill="#A9A9A9" opacity="0.4"/>
    <ellipse cx="16" cy="12" rx="2" ry="1" fill="#A9A9A9" opacity="0.3"/>
    <path d="M4,4 Q8,8 12,4 Q16,8 20,4" stroke="#A9A9A9" strokeWidth="0.5" fill="none"/>
  </svg>
);

// Ebony Wood - Very dark, almost black with minimal visible grain
export const EbonyWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="ebonyGrad" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="#2F2F2F"/>
        <stop offset="50%" stopColor="#1C1C1C"/>
        <stop offset="100%" stopColor="#0F0F0F"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#ebonyGrad)" rx="2"/>
    <path d="M4,0 L4,24 M8,0 L8,24 M12,0 L12,24 M16,0 L16,24 M20,0 L20,24"
          stroke="#404040" strokeWidth="0.3" opacity="0.5"/>
    <path d="M0,6 Q12,4 24,6 M0,12 Q12,10 24,12 M0,18 Q12,16 24,18"
          stroke="#404040" strokeWidth="0.2" fill="none" opacity="0.3"/>
    <ellipse cx="12" cy="8" rx="2" ry="0.5" fill="#404040" opacity="0.4"/>
    <ellipse cx="12" cy="16" rx="1.5" ry="0.3" fill="#404040" opacity="0.3"/>
  </svg>
);

// METAL MATERIALS - Each with unique finishes and characteristics

// Stainless Steel - Brushed finish with linear reflections
export const StainlessSteelIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="steelGrad" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="#C0C0C0"/>
        <stop offset="25%" stopColor="#E5E5E5"/>
        <stop offset="50%" stopColor="#C0C0C0"/>
        <stop offset="75%" stopColor="#A8A8A8"/>
        <stop offset="100%" stopColor="#C0C0C0"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#steelGrad)" rx="2"/>
    <path d="M0,0 L24,0 M0,2 L24,2 M0,4 L24,4 M0,6 L24,6 M0,8 L24,8 M0,10 L24,10 M0,12 L24,12 M0,14 L24,14 M0,16 L24,16 M0,18 L24,18 M0,20 L24,20 M0,22 L24,22 M0,24 L24,24"
          stroke="#FFFFFF" strokeWidth="0.1" opacity="0.3"/>
    <rect x="6" y="4" width="12" height="2" fill="#FFFFFF" opacity="0.4"/>
    <rect x="4" y="18" width="16" height="1" fill="#FFFFFF" opacity="0.3"/>
  </svg>
);

// Chrome - Mirror-like with sharp reflections and highlights
export const ChromeIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="chromeGrad" cx="30%" cy="30%" r="70%">
        <stop offset="0%" stopColor="#FFFFFF"/>
        <stop offset="30%" stopColor="#E5E5E5"/>
        <stop offset="70%" stopColor="#C0C0C0"/>
        <stop offset="100%" stopColor="#A8A8A8"/>
      </radialGradient>
      <linearGradient id="chromeRefl" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFFFFF" stopOpacity="0.8"/>
        <stop offset="50%" stopColor="transparent"/>
        <stop offset="100%" stopColor="#000000" stopOpacity="0.2"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#chromeGrad)" rx="2"/>
    <rect width="24" height="24" fill="url(#chromeRefl)" rx="2"/>
    <ellipse cx="8" cy="6" rx="3" ry="1.5" fill="#FFFFFF" opacity="0.7"/>
    <ellipse cx="16" cy="18" rx="2" ry="1" fill="#000000" opacity="0.2"/>
  </svg>
);

// Brass - Golden with patina and oxidation
export const BrassIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="brassGrad" cx="50%" cy="30%" r="70%">
        <stop offset="0%" stopColor="#FFD700"/>
        <stop offset="40%" stopColor="#B5A642"/>
        <stop offset="80%" stopColor="#8B7355"/>
        <stop offset="100%" stopColor="#6B5B47"/>
      </radialGradient>
    </defs>
    <rect width="24" height="24" fill="url(#brassGrad)" rx="2"/>
    <circle cx="12" cy="8" r="3" fill="#FFD700" opacity="0.6"/>
    <path d="M4,4 Q12,2 20,4 Q20,8 12,6 Q4,8 4,4" fill="#8B7355" opacity="0.3"/>
    <path d="M2,16 Q8,14 14,16 Q20,18 22,16" stroke="#6B5B47" strokeWidth="1" fill="none"/>
    <circle cx="6" cy="18" r="1" fill="#6B5B47" opacity="0.4"/>
    <circle cx="18" cy="20" r="0.8" fill="#6B5B47" opacity="0.3"/>
  </svg>
);

// Copper - Reddish-orange with verdigris spots
export const CopperIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="copperGrad" cx="40%" cy="40%" r="60%">
        <stop offset="0%" stopColor="#FF7F50"/>
        <stop offset="50%" stopColor="#B87333"/>
        <stop offset="100%" stopColor="#8B4513"/>
      </radialGradient>
    </defs>
    <rect width="24" height="24" fill="url(#copperGrad)" rx="2"/>
    <circle cx="8" cy="8" r="2" fill="#40E0D0" opacity="0.6"/>
    <circle cx="16" cy="16" r="1.5" fill="#40E0D0" opacity="0.5"/>
    <circle cx="20" cy="6" r="1" fill="#40E0D0" opacity="0.4"/>
    <path d="M2,12 Q8,10 14,12 Q20,14 22,12" stroke="#8B4513" strokeWidth="0.8" fill="none"/>
    <ellipse cx="12" cy="18" rx="3" ry="1" fill="#8B4513" opacity="0.3"/>
  </svg>
);

// Bronze - Dark golden-brown with aged patina
export const BronzeIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="bronzeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#CD7F32"/>
        <stop offset="50%" stopColor="#8B4513"/>
        <stop offset="100%" stopColor="#654321"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#bronzeGrad)" rx="2"/>
    <path d="M0,8 Q6,6 12,8 Q18,10 24,8 M0,16 Q6,14 12,16 Q18,18 24,16"
          stroke="#2F4F2F" strokeWidth="0.8" fill="none" opacity="0.6"/>
    <circle cx="10" cy="6" r="1.5" fill="#2F4F2F" opacity="0.5"/>
    <circle cx="14" cy="18" r="1" fill="#2F4F2F" opacity="0.4"/>
    <ellipse cx="18" cy="12" rx="2" ry="1" fill="#8B4513" opacity="0.4"/>
  </svg>
);

// Iron - Dark gray with rust spots
export const IronIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="ironGrad" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="#464451"/>
        <stop offset="50%" stopColor="#36454F"/>
        <stop offset="100%" stopColor="#2F2F2F"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#ironGrad)" rx="2"/>
    <circle cx="6" cy="6" r="1.5" fill="#8B4513" opacity="0.7"/>
    <circle cx="18" cy="14" r="1" fill="#8B4513" opacity="0.6"/>
    <circle cx="12" cy="20" r="0.8" fill="#8B4513" opacity="0.5"/>
    <path d="M2,10 Q8,8 14,10 Q20,12 22,10" stroke="#696969" strokeWidth="0.6" fill="none"/>
    <rect x="8" y="12" width="8" height="0.5" fill="#696969" opacity="0.4"/>
  </svg>
);

// STONE MATERIALS - Each with unique textures and patterns

// Marble - White with distinctive veining
export const MarbleIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="marbleGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#F8F8FF"/>
        <stop offset="30%" stopColor="#FFFFFF"/>
        <stop offset="70%" stopColor="#F5F5F5"/>
        <stop offset="100%" stopColor="#E8E8E8"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#marbleGrad)" rx="2"/>
    <path d="M2,6 Q8,4 14,8 Q20,12 22,6 M4,14 Q10,12 16,16 Q22,20 24,14"
          stroke="#D3D3D3" strokeWidth="0.8" fill="none"/>
    <path d="M0,10 Q6,8 12,12 Q18,16 24,10" stroke="#C0C0C0" strokeWidth="0.6" fill="none"/>
    <path d="M6,2 Q12,6 18,2" stroke="#B0B0B0" strokeWidth="0.4" fill="none"/>
    <path d="M2,18 Q8,16 14,20 Q20,24 22,18" stroke="#B0B0B0" strokeWidth="0.4" fill="none"/>
  </svg>
);

// Granite - Speckled with various mineral colors
export const GraniteIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#696969" rx="2"/>
    <circle cx="4" cy="4" r="0.5" fill="#2F2F2F"/>
    <circle cx="8" cy="6" r="0.3" fill="#FFFFFF"/>
    <circle cx="12" cy="3" r="0.4" fill="#8B4513"/>
    <circle cx="16" cy="7" r="0.6" fill="#2F2F2F"/>
    <circle cx="20" cy="5" r="0.3" fill="#FFFFFF"/>
    <circle cx="6" cy="10" r="0.4" fill="#8B4513"/>
    <circle cx="10" cy="12" r="0.5" fill="#2F2F2F"/>
    <circle cx="14" cy="11" r="0.3" fill="#FFFFFF"/>
    <circle cx="18" cy="13" r="0.4" fill="#8B4513"/>
    <circle cx="3" cy="16" r="0.6" fill="#2F2F2F"/>
    <circle cx="7" cy="18" r="0.3" fill="#FFFFFF"/>
    <circle cx="11" cy="17" r="0.4" fill="#8B4513"/>
    <circle cx="15" cy="19" r="0.5" fill="#2F2F2F"/>
    <circle cx="19" cy="17" r="0.3" fill="#FFFFFF"/>
    <circle cx="5" cy="22" r="0.4" fill="#8B4513"/>
    <circle cx="13" cy="21" r="0.3" fill="#FFFFFF"/>
    <circle cx="21" cy="21" r="0.5" fill="#2F2F2F"/>
  </svg>
);

// FABRIC MATERIALS - Each with unique weave patterns

// Linen - Loose weave with natural texture
export const LinenIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <pattern id="linenWeave" patternUnits="userSpaceOnUse" width="3" height="3">
        <rect width="1.5" height="1.5" fill="#F5F5DC"/>
        <rect x="1.5" y="1.5" width="1.5" height="1.5" fill="#F5F5DC"/>
        <rect x="1.5" y="0" width="1.5" height="1.5" fill="#E6E6FA" opacity="0.8"/>
        <rect x="0" y="1.5" width="1.5" height="1.5" fill="#E6E6FA" opacity="0.8"/>
      </pattern>
    </defs>
    <rect width="24" height="24" fill="url(#linenWeave)" rx="2"/>
    <path d="M0,6 L24,6 M0,12 L24,12 M0,18 L24,18" stroke="#DDD" strokeWidth="0.3" opacity="0.5"/>
    <path d="M6,0 L6,24 M12,0 L12,24 M18,0 L18,24" stroke="#DDD" strokeWidth="0.3" opacity="0.5"/>
  </svg>
);

// Velvet - Rich, plush texture with directional nap
export const VelvetIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="velvetGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#8B008B"/>
        <stop offset="30%" stopColor="#9932CC"/>
        <stop offset="70%" stopColor="#8B008B"/>
        <stop offset="100%" stopColor="#4B0082"/>
      </linearGradient>
      <filter id="velvetTexture">
        <feTurbulence baseFrequency="0.5" numOctaves="3" result="noise"/>
        <feDisplacementMap in="SourceGraphic" in2="noise" scale="0.5"/>
      </filter>
    </defs>
    <rect width="24" height="24" fill="url(#velvetGrad)" rx="2"/>
    <rect width="24" height="24" fill="url(#velvetGrad)" rx="2" filter="url(#velvetTexture)" opacity="0.7"/>
    <ellipse cx="8" cy="8" rx="2" ry="1" fill="#9932CC" opacity="0.6"/>
    <ellipse cx="16" cy="16" rx="1.5" ry="0.8" fill="#9932CC" opacity="0.5"/>
  </svg>
);

// MATERIAL ICON MAPPER - Maps material names to their unique icons
export const getUniqueMaterialIcon = (materialName: string, size: number = 48, className: string = "rounded-lg") => {
  const iconProps = { size, className };

  switch (materialName) {
    // Wood Materials
    case 'Oak Wood': return <OakWoodIcon {...iconProps} />;
    case 'Walnut Wood': return <WalnutWoodIcon {...iconProps} />;
    case 'Cherry Wood': return <CherryWoodIcon {...iconProps} />;
    case 'Maple Wood': return <MapleWoodIcon {...iconProps} />;
    case 'Birch Wood': return <BirchWoodIcon {...iconProps} />;
    case 'Pine Wood': return <PineWoodIcon {...iconProps} />;
    case 'Mahogany Wood': return <MahoganyWoodIcon {...iconProps} />;
    case 'Teak Wood': return <TeakWoodIcon {...iconProps} />;
    case 'Bamboo': return <BambooIcon {...iconProps} />;
    case 'Reclaimed Wood': return <ReclaimedWoodIcon {...iconProps} />;
    case 'Driftwood': return <DriftwoodIcon {...iconProps} />;
    case 'Ebony Wood': return <EbonyWoodIcon {...iconProps} />;

    // Metal Materials
    case 'Stainless Steel': return <StainlessSteelIcon {...iconProps} />;
    case 'Chrome': return <ChromeIcon {...iconProps} />;
    case 'Brass': return <BrassIcon {...iconProps} />;
    case 'Copper': return <CopperIcon {...iconProps} />;
    case 'Bronze': return <BronzeIcon {...iconProps} />;
    case 'Iron': return <IronIcon {...iconProps} />;

    // Stone Materials
    case 'Marble': return <MarbleIcon {...iconProps} />;
    case 'Granite': return <GraniteIcon {...iconProps} />;

    // Fabric Materials
    case 'Linen': return <LinenIcon {...iconProps} />;
    case 'Velvet': return <VelvetIcon {...iconProps} />;

    // Default fallback for materials not yet implemented
    default: return <OakWoodIcon {...iconProps} />;
  }
};
